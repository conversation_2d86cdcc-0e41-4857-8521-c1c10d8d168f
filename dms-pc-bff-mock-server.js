/**
 * DMS-PC-BFF Mock Server
 * 基于销售订单接口文档的完整 Mock 实现
 * 支持多维条件筛选和数据关联性
 */

const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Mock 数据生成器
class MockDataGenerator {
  constructor() {
    this.initializeData();
  }

  initializeData() {
    // 基础字典数据
    this.dictionaries = {
      orderStatus: [
        { code: 'draft', name: '草稿' },
        { code: 'submitted', name: '已提交' },
        { code: 'confirmed', name: '已确认' },
        { code: 'pending_delivery', name: '待交车' },
        { code: 'completed', name: '已完成' },
        { code: 'canceled', name: '已取消' }
      ],
      approvalStatus: [
        { code: 'pending', name: '待审批' },
        { code: 'approved', name: '已批准' },
        { code: 'rejected', name: '已驳回' }
      ],
      paymentStatus: [
        { code: 'unpaid', name: '未付款' },
        { code: 'partial_paid', name: '部分付款' },
        { code: 'fully_paid', name: '已付全款' },
        { code: 'refunded', name: '已退款' }
      ],
      insuranceStatus: [
        { code: 'uninsured', name: '未投保' },
        { code: 'insured', name: '已投保' },
        { code: 'expired', name: '已过期' }
      ],
      loanStatus: [
        { code: 'not_applicable', name: '不适用' },
        { code: 'pending', name: '待审批' },
        { code: 'approved', name: '审批通过' },
        { code: 'rejected', name: '审批拒绝' }
      ],
      jpjRegistrationStatus: [
        { code: 'pending', name: '待注册' },
        { code: 'in_progress', name: '注册中' },
        { code: 'registered', name: '已注册' },
        { code: 'failed', name: '注册失败' }
      ],
      customerType: [
        { code: 'individual', name: '个人' },
        { code: 'company', name: '公司' }
      ],
      paymentMethod: [
        { code: 'full_payment', name: '全款' },
        { code: 'loan', name: '贷款' }
      ],
      models: [
        { code: 'myvi', name: 'Myvi' },
        { code: 'alza', name: 'Alza' },
        { code: 'axia', name: 'Axia' },
        { code: 'bezza', name: 'Bezza' },
        { code: 'aruz', name: 'Aruz' }
      ],
      variants: {
        myvi: [
          { code: 'myvi_se', name: '1.5 SE' },
          { code: 'myvi_av', name: '1.5 AV' },
          { code: 'myvi_advance', name: '1.5 Advance' }
        ],
        alza: [
          { code: 'alza_s', name: '1.5 S' },
          { code: 'alza_h', name: '1.5 H' },
          { code: 'alza_av', name: '1.5 AV' }
        ]
      },
      colors: [
        { code: 'electric_blue', name: 'Electric Blue' },
        { code: 'granite_grey', name: 'Granite Grey' },
        { code: 'glittering_silver', name: 'Glittering Silver' },
        { code: 'ivory_white', name: 'Ivory White' },
        { code: 'lava_red', name: 'Lava Red' }
      ]
    };

    // 生成关联的订单数据
    this.orders = this.generateOrders(150);
    this.rights = this.generateRights();
  }

  generateOrders(count) {
    const orders = [];
    const regions = ['Central', 'Northern', 'Southern', 'Eastern'];
    const cities = ['Kuala Lumpur', 'Penang', 'Johor Bahru', 'Kuantan'];
    const stores = [
      'Perodua KL Sentral', 'Perodua Penang', 'Perodua JB Plaza',
      'Perodua Kuantan', 'Perodua Shah Alam', 'Perodua Ipoh'
    ];
    const salesConsultants = [
      'Ali bin Abu', 'Siti Aminah', 'Raj Kumar', 'Lim Wei Ming',
      'Ahmad Farid', 'Nurul Huda'
    ];

    for (let i = 1; i <= count; i++) {
      const model = this.getRandomItem(this.dictionaries.models);
      const variant = this.getRandomItem(this.dictionaries.variants[model.code] || []);
      const color = this.getRandomItem(this.dictionaries.colors);
      const customerType = this.getRandomItem(this.dictionaries.customerType);
      const paymentMethod = this.getRandomItem(this.dictionaries.paymentMethod);

      // 根据支付方式决定相关状态
      const loanStatus = paymentMethod.code === 'loan'
        ? this.getRandomItem(this.dictionaries.loanStatus.slice(1)) // 排除不适用
        : this.dictionaries.loanStatus[0]; // 不适用

      const orderStatus = this.getRandomItem(this.dictionaries.orderStatus);
      const approvalStatus = this.getRandomItem(this.dictionaries.approvalStatus);
      const paymentStatus = this.getRandomItem(this.dictionaries.paymentStatus);
      const insuranceStatus = this.getRandomItem(this.dictionaries.insuranceStatus);
      const jpjStatus = this.getRandomItem(this.dictionaries.jpjRegistrationStatus);

      // 生成客户信息
      const customerNames = [
        '张三', '李四', '王五', '赵六', '陈七', '刘八', '黄九', '林十',
        'Ahmad Ali', 'Siti Fatimah', 'Raj Patel', 'Lim Ah Kow'
      ];
      const customerName = this.getRandomItem(customerNames);

      // 计算金额（有关联性）
      const basePrice = this.getBasePrice(model.code, variant?.code);
      const accessories = this.generateAccessories();
      const accessoriesAmount = accessories.reduce((sum, acc) => sum + acc.total, 0);
      const insuranceAmount = Math.floor(Math.random() * 2000) + 1000;
      const otrAmount = Math.floor(Math.random() * 500) + 200;
      const orderTotalAmount = basePrice + 100 + accessoriesAmount + insuranceAmount + otrAmount; // +100 车牌费

      const order = {
        id: i,
        orderNo: `ORD${String(i).padStart(6, '0')}`,
        createdAt: this.getRandomDate(),
        ordererName: customerName,
        ordererPhone: this.generatePhone(),
        customerName: customerName,
        customerPhone: this.generatePhone(),
        customerType: customerType.name,
        customerTypeCode: customerType.code,
        idType: '身份证',
        idNumber: this.generateIdNumber(),
        email: `${customerName.toLowerCase().replace(/\s+/g, '')}@email.com`,
        address: this.generateAddress(),
        state: this.getRandomItem(cities),
        city: this.getRandomItem(cities),
        zipCode: String(Math.floor(Math.random() * 90000) + 10000),
        model: model.name,
        modelCode: model.code,
        variant: variant?.name || '',
        variantCode: variant?.code || '',
        color: color.name,
        colorCode: color.code,
        vin: this.generateVIN(),
        paymentMethod: paymentMethod.name,
        paymentMethodCode: paymentMethod.code,
        orderStatus: orderStatus.name,
        orderStatusCode: orderStatus.code,
        approvalStatus: approvalStatus.name,
        approvalStatusCode: approvalStatus.code,
        paymentStatus: paymentStatus.name,
        paymentStatusCode: paymentStatus.code,
        insuranceStatus: insuranceStatus.name,
        insuranceStatusCode: insuranceStatus.code,
        loanStatus: loanStatus.name,
        loanStatusCode: loanStatus.code,
        jpjRegistrationStatus: jpjStatus.name,
        jpjRegistrationStatusCode: jpjStatus.code,

        // 门店信息
        region: this.getRandomItem(regions),
        storeCity: this.getRandomItem(cities),
        storeName: this.getRandomItem(stores),
        salesConsultant: this.getRandomItem(salesConsultants),

        // 金额信息
        vehiclePrice: basePrice,
        numberPlatesFee: 100,
        orderTotalAmount: orderTotalAmount,
        optionalPartsAmount: accessoriesAmount,
        insuranceAmount: insuranceAmount,
        ortTotalAmount: otrAmount,
        promotionDiscountAmount: 0,
        remainingReceivableAmount: Math.floor(orderTotalAmount * 0.2), // 假设已付80%

        // 贷款信息
        depositAmount: paymentMethod.code === 'loan' ? 5000 : orderTotalAmount * 0.1,
        loanAmount: paymentMethod.code === 'loan' ? orderTotalAmount * 0.8 : 0,
        loanTerm: paymentMethod.code === 'loan' ? this.getRandomItem([36, 48, 60, 72]) : null,
        finalPaymentAmount: paymentMethod.code === 'full_payment' ? orderTotalAmount * 0.9 : orderTotalAmount * 0.2,

        // 开票信息
        invoiceType: customerType.code === 'individual' ? '个人发票' : '公司发票',
        invoiceName: customerName,
        invoicePhone: this.generatePhone(),
        invoiceAddress: this.generateAddress(),

        // 详细数据（用于详情页面）
        accessories: accessories,
        rights: this.generateOrderRights(),
        policies: this.generatePolicies(),
        otrFees: this.generateOtrFees(),
        changeRecords: this.generateChangeRecords(),
        insuranceRemarks: '客户要求尽快处理保险事宜。'
      };

      orders.push(order);
    }

    return orders;
  }

  getBasePrice(modelCode, variantCode) {
    const prices = {
      'myvi_se': 54000,
      'myvi_av': 58000,
      'myvi_advance': 62000,
      'alza_s': 68000,
      'alza_h': 72000,
      'alza_av': 76000
    };
    return prices[variantCode] || 50000;
  }

  generateAccessories() {
    const accessories = [
      { category: '电子产品', name: '行车记录仪', price: 300 },
      { category: '电子产品', name: '倒车雷达', price: 250 },
      { category: '装饰用品', name: '脚垫套装', price: 150 },
      { category: '装饰用品', name: '座椅套', price: 200 },
      { category: '安全用品', name: '儿童安全座椅', price: 400 }
    ];

    const selected = [];
    const count = Math.floor(Math.random() * 3) + 1; // 1-3个配件

    for (let i = 0; i < count; i++) {
      const accessory = this.getRandomItem(accessories);
      const quantity = Math.floor(Math.random() * 2) + 1;
      selected.push({
        ...accessory,
        quantity: quantity,
        total: accessory.price * quantity
      });
    }

    return selected;
  }

  generateRights() {
    return [
      {
        id: 1,
        code: 'D001',
        name: '新车折扣',
        mode: '折扣',
        discountPrice: 200.00,
        effectiveDate: '2023-10-27',
        expiryDate: '2023-11-27'
      },
      {
        id: 2,
        code: 'G002',
        name: '免费保养一次',
        mode: '赠品',
        discountPrice: 0.00,
        effectiveDate: '2023-10-27',
        expiryDate: '2024-10-26'
      },
      {
        id: 3,
        code: 'D003',
        name: '配件优惠',
        mode: '折扣',
        discountPrice: 150.00,
        effectiveDate: '2023-10-01',
        expiryDate: '2023-12-31'
      },
      {
        id: 4,
        code: 'G004',
        name: '免费贴膜',
        mode: '赠品',
        discountPrice: 0.00,
        effectiveDate: '2023-10-01',
        expiryDate: '2024-03-31'
      },
      {
        id: 5,
        code: 'D005',
        name: '首付优惠',
        mode: '折扣',
        discountPrice: 500.00,
        effectiveDate: '2023-11-01',
        expiryDate: '2023-12-31'
      }
    ];
  }

  generateOrderRights() {
    const allRights = this.generateRights();
    const count = Math.floor(Math.random() * 3); // 0-2个权益
    const selected = [];

    for (let i = 0; i < count; i++) {
      const right = this.getRandomItem(allRights);
      if (!selected.find(r => r.id === right.id)) {
        selected.push(right);
      }
    }

    return selected;
  }

  // 辅助方法
  getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  getRandomDate() {
    const start = new Date(2023, 0, 1);
    const end = new Date();
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  }

  generatePhone() {
    return `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`;
  }

  generateIdNumber() {
    return `${Math.floor(Math.random() * 900000) + 100000}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000) + 1000}`;
  }

  generateVIN() {
    return `PM2BA${String(Math.floor(Math.random() * 1000000000000)).padStart(12, '0')}`;
  }

  generateAddress() {
    const addresses = [
      '123, Jalan ABC, Taman DEF, 50000 Kuala Lumpur',
      '456, Lorong XYZ, Taman GHI, 10200 Penang',
      '789, Jalan PQR, Taman STU, 80000 Johor Bahru'
    ];
    return this.getRandomItem(addresses);
  }

  generatePolicies() {
    return [
      {
        policyNumber: `P${Math.floor(Math.random() * 1000000000)}`,
        insuranceType: '综合险',
        insuranceCompany: 'ABC Insurance',
        effectiveDate: '2023-10-28',
        expiryDate: '2024-10-27',
        price: 1500.00
      }
    ];
  }

  generateOtrFees() {
    return [
      {
        invoiceNumber: `JPJ${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
        item: '首次注册',
        price: 300.00,
        effectiveDate: '2023-10-28',
        expiryDate: null
      }
    ];
  }

  generateChangeRecords() {
    return [
      {
        operationTime: '2023-10-28T11:00:00',
        operatorName: 'Admin',
        originalContent: '颜色: Granite Grey',
        changedContent: '颜色: Electric Blue'
      }
    ];
  }
}

// 初始化数据生成器
const mockData = new MockDataGenerator();

// 工具函数
const createResponse = (data, message = '操作成功') => ({
  success: true,
  code: '10000000',
  message,
  traceId: `trace-${Date.now()}`,
  timestamp: Date.now(),
  result: data
});

const createErrorResponse = (code, message) => ({
  success: false,
  code,
  message,
  traceId: `trace-${Date.now()}`,
  timestamp: Date.now(),
  result: null
});

// 筛选工具函数
const filterOrders = (orders, query) => {
  return orders.filter(order => {
    // 文本筛选
    if (query.customerName && !order.customerName.includes(query.customerName)) return false;
    if (query.customerPhone && !order.customerPhone.includes(query.customerPhone)) return false;
    if (query.ordererName && !order.ordererName.includes(query.ordererName)) return false;
    if (query.ordererPhone && !order.ordererPhone.includes(query.ordererPhone)) return false;
    if (query.orderNo && !order.orderNo.includes(query.orderNo)) return false;

    // 下拉筛选
    if (query.customerType && order.customerTypeCode !== query.customerType) return false;
    if (query.model && order.modelCode !== query.model) return false;
    if (query.orderStatus && order.orderStatusCode !== query.orderStatus) return false;
    if (query.approvalStatus && order.approvalStatusCode !== query.approvalStatus) return false;
    if (query.paymentStatus && order.paymentStatusCode !== query.paymentStatus) return false;
    if (query.insuranceStatus && order.insuranceStatusCode !== query.insuranceStatus) return false;
    if (query.loanStatus && order.loanStatusCode !== query.loanStatus) return false;
    if (query.jpjRegistrationStatus && order.jpjRegistrationStatusCode !== query.jpjRegistrationStatus) return false;

    // 时间范围筛选
    if (query.createTimeStart) {
      const startDate = new Date(query.createTimeStart);
      if (new Date(order.createdAt) < startDate) return false;
    }
    if (query.createTimeEnd) {
      const endDate = new Date(query.createTimeEnd);
      if (new Date(order.createdAt) > endDate) return false;
    }

    return true;
  });
};

// 分页工具函数
const paginate = (data, pageNum = 1, pageSize = 10) => {
  const total = data.length;
  const pages = Math.ceil(total / pageSize);
  const current = parseInt(pageNum);
  const size = parseInt(pageSize);
  const start = (current - 1) * size;
  const end = start + size;
  const records = data.slice(start, end);

  return {
    records,
    total,
    size,
    current,
    pages
  };
};

// ==================== API 路由实现 ====================

// 1. 订单列表查询接口
app.get('/api/v1/orders', (req, res) => {
  try {
    const query = req.query;
    console.log('订单列表查询参数:', query);

    // 筛选数据
    const filteredOrders = filterOrders(mockData.orders, query);

    // 分页处理
    const pageNum = parseInt(query.pageNum) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const paginatedData = paginate(filteredOrders, pageNum, pageSize);

    // 返回列表格式的数据（简化字段）
    const listData = {
      ...paginatedData,
      records: paginatedData.records.map(order => ({
        id: order.id,
        orderNo: order.orderNo,
        createdAt: order.createdAt,
        ordererName: order.ordererName,
        ordererPhone: order.ordererPhone,
        customerName: order.customerName,
        customerPhone: order.customerPhone,
        customerType: order.customerType,
        model: order.model,
        variant: order.variant,
        color: order.color,
        vin: order.vin,
        paymentMethod: order.paymentMethod,
        orderStatus: order.orderStatus,
        approvalStatus: order.approvalStatus,
        paymentStatus: order.paymentStatus,
        insuranceStatus: order.insuranceStatus,
        jpjRegistrationStatus: order.jpjRegistrationStatus,
        orderTotalAmount: order.orderTotalAmount
      }))
    };

    res.json(createResponse(listData, '查询成功'));
  } catch (error) {
    console.error('订单列表查询错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 2. 订单详情查询接口
app.get('/api/v1/orders/:orderNo', (req, res) => {
  try {
    const { orderNo } = req.params;
    console.log('订单详情查询:', orderNo);

    const order = mockData.orders.find(o => o.orderNo === orderNo);

    if (!order) {
      return res.json(createErrorResponse('20000001', '订单不存在'));
    }

    // 返回完整的订单详情
    const detailData = {
      // 订单概要信息
      orderNo: order.orderNo,
      createdAt: order.createdAt,
      orderStatus: order.orderStatus,
      approvalStatus: order.approvalStatus,
      paymentStatus: order.paymentStatus,
      insuranceStatus: order.insuranceStatus,
      jpjRegistrationStatus: order.jpjRegistrationStatus,

      // 客户信息
      ordererName: order.ordererName,
      ordererPhone: order.ordererPhone,
      customerName: order.customerName,
      customerPhone: order.customerPhone,
      idType: order.idType,
      idNumber: order.idNumber,
      email: order.email,
      address: order.address,
      state: order.state,
      city: order.city,
      zipCode: order.zipCode,
      customerType: order.customerType,

      // 购车门店信息
      region: order.region,
      storeCity: order.storeCity,
      storeName: order.storeName,
      salesConsultant: order.salesConsultant,

      // 购车信息 - 车辆信息
      model: order.model,
      variant: order.variant,
      color: order.color,
      vehiclePrice: order.vehiclePrice,
      numberPlatesFee: order.numberPlatesFee,
      vin: order.vin,
      accessories: order.accessories,

      // 购车信息 - 开票信息
      invoiceType: order.invoiceType,
      invoiceName: order.invoiceName,
      invoicePhone: order.invoicePhone,
      invoiceAddress: order.invoiceAddress,

      // 购车信息 - 权益信息
      rights: order.rights,

      // 购车信息 - 支付信息
      paymentMethod: order.paymentMethod,
      loanStatus: order.loanStatus,
      depositAmount: order.depositAmount,
      loanAmount: order.loanAmount,
      loanTerm: order.loanTerm,
      finalPaymentAmount: order.finalPaymentAmount,

      // 购车信息 - 保险信息
      policies: order.policies,
      insuranceRemarks: order.insuranceRemarks,

      // 购车信息 - OTR费用信息
      otrFees: order.otrFees,

      // 订单变更记录
      changeRecords: order.changeRecords,

      // 金额汇总
      orderTotalAmount: order.orderTotalAmount,
      remainingReceivableAmount: order.remainingReceivableAmount,
      optionalPartsAmount: order.optionalPartsAmount,
      insuranceAmount: order.insuranceAmount,
      promotionDiscountAmount: order.promotionDiscountAmount,
      ortTotalAmount: order.ortTotalAmount
    };

    res.json(createResponse(detailData, '查询成功'));
  } catch (error) {
    console.error('订单详情查询错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 3. 订单编辑信息查询接口
app.get('/api/v1/orders/:orderNo/edit', (req, res) => {
  try {
    const { orderNo } = req.params;
    console.log('订单编辑信息查询:', orderNo);

    const order = mockData.orders.find(o => o.orderNo === orderNo);

    if (!order) {
      return res.json(createErrorResponse('20000001', '订单不存在'));
    }

    // 检查订单状态是否允许编辑
    if (order.orderStatusCode === 'completed' || order.orderStatusCode === 'canceled') {
      return res.json(createErrorResponse('20000002', '订单状态不允许编辑'));
    }

    // 返回编辑页面所需数据（区分只读和可编辑字段）
    const editData = {
      // 只读信息 - 订单概要
      orderNo: order.orderNo,
      createdAt: order.createdAt,

      // 只读信息 - 客户信息
      ordererName: order.ordererName,
      ordererPhone: order.ordererPhone,
      customerName: order.customerName,
      customerPhone: order.customerPhone,
      idType: order.idType,
      idNumber: order.idNumber,
      email: order.email,
      address: order.address,
      state: order.state,
      city: order.city,
      zipCode: order.zipCode,
      customerType: order.customerType,

      // 只读信息 - 购车门店信息
      region: order.region,
      storeCity: order.storeCity,
      storeName: order.storeName,
      salesConsultant: order.salesConsultant,

      // 可编辑信息 - 车辆信息
      model: order.model, // 只读
      variant: order.variant, // 只读
      color: order.color, // 可编辑
      colorCode: order.colorCode, // 可编辑
      vehiclePrice: order.vehiclePrice, // 只读
      numberPlatesFee: order.numberPlatesFee, // 只读
      vin: order.vin, // 只读
      accessories: order.accessories, // 只读

      // 只读信息 - 开票信息
      invoiceType: order.invoiceType,
      invoiceName: order.invoiceName,
      invoicePhone: order.invoicePhone,
      invoiceAddress: order.invoiceAddress,

      // 可编辑信息 - 权益信息
      rights: order.rights,

      // 可编辑信息 - 支付信息
      paymentMethod: order.paymentMethod, // 可编辑
      paymentMethodCode: order.paymentMethodCode, // 可编辑
      loanStatus: order.loanStatus, // 可编辑
      loanStatusCode: order.loanStatusCode, // 可编辑
      depositAmount: order.depositAmount, // 只读
      loanAmount: order.loanAmount, // 可编辑
      loanTerm: order.loanTerm, // 可编辑
      finalPaymentAmount: order.finalPaymentAmount, // 只读

      // 可编辑信息 - 保险信息
      policies: order.policies, // 只读
      insuranceRemarks: order.insuranceRemarks, // 可编辑

      // 只读信息 - OTR费用
      otrFees: order.otrFees,

      // 金额汇总
      orderTotalAmount: order.orderTotalAmount,
      remainingReceivableAmount: order.remainingReceivableAmount,
      optionalPartsAmount: order.optionalPartsAmount,
      insuranceAmount: order.insuranceAmount,
      promotionDiscountAmount: order.promotionDiscountAmount,
      ortTotalAmount: order.ortTotalAmount
    };

    res.json(createResponse(editData, '查询成功'));
  } catch (error) {
    console.error('订单编辑信息查询错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 4. 订单更新接口
app.put('/api/v1/orders/:orderNo', (req, res) => {
  try {
    const { orderNo } = req.params;
    const updateData = req.body;
    console.log('订单更新:', orderNo, updateData);

    const orderIndex = mockData.orders.findIndex(o => o.orderNo === orderNo);

    if (orderIndex === -1) {
      return res.json(createErrorResponse('20000001', '订单不存在'));
    }

    const order = mockData.orders[orderIndex];

    // 检查订单状态是否允许编辑
    if (order.orderStatusCode === 'completed' || order.orderStatusCode === 'canceled') {
      return res.json(createErrorResponse('20000002', '订单状态不允许编辑'));
    }

    // 模拟金额校验
    if (updateData.orderTotalAmount) {
      const expectedTotal = order.vehiclePrice + order.numberPlatesFee +
                           order.optionalPartsAmount + order.insuranceAmount + order.ortTotalAmount;
      if (Math.abs(updateData.orderTotalAmount - expectedTotal) > 1) {
        return res.json(createErrorResponse('20000006', '金额校验失败'));
      }
    }

    // 更新订单数据
    if (updateData.color) {
      const colorOption = mockData.dictionaries.colors.find(c => c.name === updateData.color);
      if (colorOption) {
        order.color = colorOption.name;
        order.colorCode = colorOption.code;

        // 添加变更记录
        order.changeRecords.unshift({
          operationTime: new Date().toISOString(),
          operatorName: 'Current User',
          originalContent: `颜色: ${order.color}`,
          changedContent: `颜色: ${updateData.color}`
        });
      }
    }

    if (updateData.paymentMethod) {
      order.paymentMethod = updateData.paymentMethod;
      order.paymentMethodCode = updateData.paymentMethodCode;
    }

    if (updateData.loanStatus) {
      order.loanStatus = updateData.loanStatus;
      order.loanStatusCode = updateData.loanStatusCode;
    }

    if (updateData.loanAmount !== undefined) {
      order.loanAmount = updateData.loanAmount;
    }

    if (updateData.loanTerm !== undefined) {
      order.loanTerm = updateData.loanTerm;
    }

    if (updateData.insuranceRemarks !== undefined) {
      order.insuranceRemarks = updateData.insuranceRemarks;
    }

    // 更新权益信息
    if (updateData.rightIds) {
      const selectedRights = mockData.rights.filter(r => updateData.rightIds.includes(r.id));
      order.rights = selectedRights;

      // 重新计算权益优惠金额
      order.promotionDiscountAmount = selectedRights.reduce((sum, right) => sum + right.discountPrice, 0);
    }

    // 更新金额信息（如果前端提供了计算结果）
    if (updateData.orderTotalAmount !== undefined) {
      order.orderTotalAmount = updateData.orderTotalAmount;
    }
    if (updateData.orderNetAmount !== undefined) {
      order.orderNetAmount = updateData.orderNetAmount;
    }
    if (updateData.remainingReceivableAmount !== undefined) {
      order.remainingReceivableAmount = updateData.remainingReceivableAmount;
    }
    if (updateData.rightsDiscountAmount !== undefined) {
      order.promotionDiscountAmount = updateData.rightsDiscountAmount;
    }

    // 更新时间戳
    order.updatedAt = new Date().toISOString();

    res.json(createResponse(null, '更新成功'));
  } catch (error) {
    console.error('订单更新错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 5. 可选权益列表查询接口
app.get('/api/v1/rights/available', (req, res) => {
  try {
    const query = req.query;
    console.log('权益列表查询参数:', query);

    let filteredRights = [...mockData.rights];

    // 搜索筛选
    if (query.code) {
      filteredRights = filteredRights.filter(right =>
        right.code.toLowerCase().includes(query.code.toLowerCase())
      );
    }

    if (query.name) {
      filteredRights = filteredRights.filter(right =>
        right.name.toLowerCase().includes(query.name.toLowerCase())
      );
    }

    // 权益查询不使用分页，返回完整列表
    res.json(createResponse(filteredRights, '查询成功'));
  } catch (error) {
    console.error('权益列表查询错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 6. 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'dms-pc-bff-mock',
    data: {
      orders: mockData.orders.length,
      rights: mockData.rights.length
    }
  });
});

// 7. 数据重置接口（开发调试用）
app.post('/api/v1/mock/reset', (req, res) => {
  try {
    mockData.initializeData();
    res.json(createResponse(null, '数据重置成功'));
  } catch (error) {
    console.error('数据重置错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 8. 获取字典数据接口（模拟字典服务）
app.get('/api/v1/dictionaries/:type', (req, res) => {
  try {
    const { type } = req.params;
    console.log('字典数据查询:', type);

    const dictData = mockData.dictionaries[type];

    if (!dictData) {
      return res.json(createErrorResponse('20000003', '字典类型不存在'));
    }

    res.json(createResponse(dictData, '查询成功'));
  } catch (error) {
    console.error('字典数据查询错误:', error);
    res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
  }
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json(createErrorResponse('50000001', '系统内部错误'));
});

// 404 处理
app.use((req, res) => {
  res.status(404).json(createErrorResponse('40400001', '接口不存在'));
});

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log('='.repeat(60));
  console.log('🚀 DMS-PC-BFF Mock Server 启动成功!');
  console.log(`📡 服务地址: http://localhost:${PORT}`);
  console.log(`📊 数据统计: ${mockData.orders.length} 条订单, ${mockData.rights.length} 条权益`);
  console.log('='.repeat(60));
  console.log('📋 可用接口:');
  console.log('  GET  /api/v1/orders                    - 订单列表查询');
  console.log('  GET  /api/v1/orders/:orderNo           - 订单详情查询');
  console.log('  GET  /api/v1/orders/:orderNo/edit      - 订单编辑信息查询');
  console.log('  PUT  /api/v1/orders/:orderNo           - 订单更新');
  console.log('  GET  /api/v1/rights/available          - 可选权益列表查询');
  console.log('  GET  /api/v1/dictionaries/:type        - 字典数据查询');
  console.log('  GET  /health                           - 健康检查');
  console.log('  POST /api/v1/mock/reset                - 数据重置');
  console.log('='.repeat(60));
  console.log('💡 示例请求:');
  console.log(`  curl "http://localhost:${PORT}/api/v1/orders?pageNum=1&pageSize=10"`);
  console.log(`  curl "http://localhost:${PORT}/api/v1/orders/ORD000001"`);
  console.log(`  curl "http://localhost:${PORT}/api/v1/rights/available"`);
  console.log('='.repeat(60));
});
