### **AIDAD P2A 工作流：全流程提示词（Prompts）**

#### **第一步：页面解构与精准提取 (生成 P01)**

当您准备好开始时，请使用以下提示词模板向我发起任务。

**▶️ 提示词模板 (Step 1)**

```text
# 指令：启动 P2A 工作流 - 步骤 1/3

我们现在开始执行 `L03-页面转接口流程.md` 中定义的 **步骤 1: 页面解构与精准提取**。

**目标**: 创建 `P01: 页面元素与数据分析清单`。

**模式**: **精密模式** (请严格按照输入文件进行1:1的提取，不要进行任何设计或推断)。

**输入文件**:
1.  `[请在这里粘贴或指定前端Vue组件的文件路径，例如：src/views/AppointmentManagementView.vue]`
2.  `[如果有关联的需求文档，请指定路径，例如：docs/AppointmentRequirements.md]`
3.  `[如果有API设计规范等格式参考，请指定路径]`

请开始分析，并在完成后向我提交 `P01` 的草案。
```

---

#### **第二步：实体建模与关系梳理 (生成 P02)**

在您审核并确认我提交的 `P01` 草案无误后，使用此提示词进入第二阶段。

**▶️ 提示词模板 (Step 2)**

```text
# 指令：P2A 工作流 - 步骤 2/3

`P01: 页面元素与数据分析清单` 已确认无误，内容完整。

现在我们进入 **步骤 2: 实体建模与关系梳理**。

**目标**: 基于 `P01` 创建 `P02: 领域实体模型` (包含实体定义、关系描述和E-R图)。

**模式**: **创意模式** (现在你可以扮演架构师的角色，对 `P01` 的原始数据进行归纳、抽象和设计)。

**核心输入**:
1.  刚刚定稿的 `P01` 文档。
2.  `[如果有关联的数据库设计规范，请指定路径]`

请开始分析，并向我提交 `P02` 的草案，特别是实体划分的建议和E-R图，供我评审。
```

---

#### **第三步：接口契约生成与交叉验证 (生成 P03)**

在您（人类专家）与我（AI顾问）共同协作，评审并最终定稿 `P02` 之后，使用此提示词发起最后一步，生成最终交付物。

**▶️ 提示词模板 (Step 3)**

```text
# 指令：P2A 工作流 - 步骤 3/3 (最终交付)

`P02: 领域实体模型` 已审查并最终定稿。它现在是本次设计的**唯一事实来源 (Single Source of Truth)**。

现在我们进入最后一步 **步骤 3: 接口契约生成与交叉验证**。

**目标**: 创建最终的 `P03: API接口契约`。

**模式**: **精密模式** (你的任务是严格执行规则，而不是创造)。

**核心指令**:
1.  以定稿的 `P02` 为核心资产和唯一标尺。
2.  以定稿的 `P01` (特别是用户动作清单) 为蓝图。
3.  **执行交叉验证**: 遍历 `P01` 中的每个用户动作，为其生成API接口。在生成过程中，接口的所有字段（特别是`归属表`列）和数据传输对象（DTO）的结构，**必须** 严格与 `P02` 中定义的实体保持一致。

**输入文件**:
1.  `P01 (定稿)`
2.  `P02 (定稿)`
3.  `[请提供API设计规范，例如：公司标准的JSON返回结构]`

请开始执行，并向我提交最终的 `P03` 文档。
``` 