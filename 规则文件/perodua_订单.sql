
-- ----------------------------
-- Table structure for tc_intent_level_config
-- ----------------------------
DROP TABLE IF EXISTS `tc_intent_level_config`;
CREATE TABLE `tc_intent_level_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，配置ID',
  `intent_level` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '意向级别，如：H,A,B,C',
  `follow_up_duration_hours` int NOT NULL COMMENT '跟进时限，单位为小时',
  `definition_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '定义描述',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_intent_level` (`intent_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='意向级别配置字典表';


-- ----------------------------
-- Table structure for tt_customer_signature
-- ----------------------------
DROP TABLE IF EXISTS `tt_customer_signature`;
CREATE TABLE `tt_customer_signature` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '签字照片主键ID',
  `delivery_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联交车单号',
  `file_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通用文件服务的文件ID',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_delivery_number` (`delivery_number`),
  KEY `idx_file_id` (`file_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户签字照片表';

-- ----------------------------
-- Table structure for tt_defeat_application
-- ----------------------------
DROP TABLE IF EXISTS `tt_defeat_application`;
CREATE TABLE `tt_defeat_application` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，申请ID',
  `store_prospect_id` bigint NOT NULL COMMENT '门店潜客ID，关联tt_store_prospect.id',
  `applicant_id` bigint NOT NULL COMMENT '申请人ID（销售顾问）',
  `application_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `defeat_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '战败原因，如：价格原因、竞品原因',
  `detailed_description` text COLLATE utf8mb4_unicode_ci COMMENT '销售顾问填写的详细说明',
  `audit_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核状态，如：待审核、已通过、已驳回',
  `auditor_id` bigint DEFAULT NULL COMMENT '审核人ID（销售经理）',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `audit_comments` text COLLATE utf8mb4_unicode_ci COMMENT '销售经理填写，如驳回原因',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `store_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_store_prospect_id` (`store_prospect_id`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='战败申请表';

-- ----------------------------
-- Table structure for tt_delivery_order
-- ----------------------------
DROP TABLE IF EXISTS `tt_delivery_order`;
CREATE TABLE `tt_delivery_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '交车单主键ID',
  `delivery_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交车单号',
  `order_id` bigint NOT NULL COMMENT '关联订单ID',
  `order_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号',
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购车人名称',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购车人手机号',
  `vin` varchar(17) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆识别代号',
  `model` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车型',
  `variant` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置',
  `color` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '颜色',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `dealer_store` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店名称',
  `sales_consultant_id` bigint NOT NULL COMMENT '销售顾问ID',
  `sales_consultant` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销售顾问姓名',
  `order_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单状态码',
  `invoice_time` timestamp NULL DEFAULT NULL COMMENT '开票时间',
  `delivery_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交车状态码',
  `customer_confirmed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '客户是否确认',
  `confirmation_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '确认类型码',
  `customer_confirm_time` timestamp NULL DEFAULT NULL COMMENT '客户确认时间',
  `delivery_time` timestamp NULL DEFAULT NULL COMMENT '实际交车时间',
  `submit_confirm_time` timestamp NULL DEFAULT NULL COMMENT '提交确认时间',
  `submit_confirm_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提交确认人',
  `delivery_notes` text COLLATE utf8mb4_unicode_ci COMMENT '交车备注',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `signature_photo` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '签字照片路径',
  `delivery_confirm_time` timestamp NULL DEFAULT NULL COMMENT '交车确认时间',
  `delivery_confirm_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_delivery_number` (`delivery_number`),
  KEY `idx_delivery_status` (`delivery_status`),
  KEY `idx_store_consultant` (`store_id`,`sales_consultant_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_vin` (`vin`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交车单表';


-- ----------------------------
-- Table structure for tt_factory_overview_daily_stats
-- ----------------------------
DROP TABLE IF EXISTS `tt_factory_overview_daily_stats`;
CREATE TABLE `tt_factory_overview_daily_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `big_region` varchar(50) DEFAULT NULL COMMENT '所属大区',
  `small_region` varchar(50) DEFAULT NULL COMMENT '所属小区',
  `store_id` varchar(50) DEFAULT NULL COMMENT '所属门店ID',
  `total_lead_count` int DEFAULT '0' COMMENT '总线索数',
  `h_level_prospect_count` int DEFAULT '0' COMMENT 'H级门店潜客数',
  `monthly_conversion_prospect_count` int DEFAULT '0' COMMENT '本月转化门店潜客数',
  `cross_store_customer_count` int DEFAULT '0' COMMENT '跨门店客户数',
  `last_month_total_lead_count` int DEFAULT '0' COMMENT '上月总线索数',
  `last_month_h_level_prospect_count` int DEFAULT '0' COMMENT '上月H级门店潜客数',
  `current_month_new_prospect_count` int DEFAULT '0' COMMENT '本月新分配潜客数',
  `last_month_remaining_prospect_count` int DEFAULT '0' COMMENT '上月结转潜客数',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_big_region` (`big_region`),
  KEY `idx_small_region` (`small_region`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='厂端潜客总览每日统计数据表';

-- ----------------------------
-- Table structure for tt_follow_up_record
-- ----------------------------
DROP TABLE IF EXISTS `tt_follow_up_record`;
CREATE TABLE `tt_follow_up_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，记录ID',
  `store_prospect_id` bigint NOT NULL COMMENT '门店潜客ID，关联tt_store_prospect.id',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `sales_advisor_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销售顾问ID',
  `follow_up_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '跟进方式，如：电话、社交软件、到店面谈',
  `follow_up_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '跟进发生的时间',
  `intent_level_after_follow_up` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '本次跟进后更新的意向级别',
  `follow_up_details` text COLLATE utf8mb4_unicode_ci COMMENT '对本次跟进过程和结果的描述',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_store_prospect_id` (`store_prospect_id`),
  KEY `idx_follow_up_time` (`follow_up_time`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='门店潜客跟进记录表';

-- ----------------------------
-- Table structure for tt_invoice
-- ----------------------------
DROP TABLE IF EXISTS `tt_invoice`;
CREATE TABLE `tt_invoice` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '发票ID',
  `order_id` bigint NOT NULL COMMENT '订单ID，关联tt_order.id',
  `invoice_number` varchar(50) NOT NULL COMMENT '发票号码',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  `invoice_status` enum('DRAFT','PENDING_CONFIRM','CONFIRMED','PRINTED','SENT','VOIDED') NOT NULL DEFAULT 'DRAFT' COMMENT '发票状态：草稿/待确认/已确认/已打印/已发送/已作废',
  `invoice_type` enum('NORMAL','RED_INVOICE') NOT NULL DEFAULT 'NORMAL' COMMENT '发票类型：正常发票/红字发票',
  `original_invoice_id` bigint DEFAULT NULL COMMENT '原发票ID（红字发票时使用）',
  `dealer_id` bigint NOT NULL COMMENT '门店ID',
  `dealer_code` varchar(20) NOT NULL COMMENT '门店代码（冗余）',
  `dealer_name` varchar(100) NOT NULL COMMENT '门店名称（冗余）',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `subtotal` decimal(15,2) NOT NULL COMMENT '小计金额',
  `tax_amount` decimal(15,2) NOT NULL COMMENT '税金金额',
  `discount_amount` decimal(15,2) DEFAULT '0.00' COMMENT '折扣金额',
  `adjustment_amount` decimal(15,2) DEFAULT '0.00' COMMENT '调整金额（支持负数）',
  `total_amount` decimal(15,2) NOT NULL COMMENT '总金额',
  `invoice_date` date NOT NULL COMMENT '发票日期',
  `printed_at` datetime DEFAULT NULL COMMENT '打印时间',
  `sent_at` datetime DEFAULT NULL COMMENT '发送时间',
  `invoice_items_snapshot` longtext COMMENT '发票明细数据快照（JSON格式，生成时落库，不可修改）',
  `remarks` text COMMENT '备注',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `updated_by` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invoice_number` (`invoice_number`),
  KEY `idx_dealer_id` (`dealer_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_invoice_date` (`invoice_date`),
  KEY `idx_invoice_status` (`invoice_status`),
  KEY `idx_original_invoice_id` (`original_invoice_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发票主表';

-- ----------------------------
-- Table structure for tt_invoice_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `tt_invoice_operation_log`;
CREATE TABLE `tt_invoice_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `operation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `operation_module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作模块（如：invoice, customer, vehicle等）',
  `operation_detail` varchar(200) DEFAULT NULL COMMENT '操作详情描述',
  `target_type` varchar(50) NOT NULL COMMENT '目标对象类型',
  `target_id` bigint NOT NULL COMMENT '目标对象ID',
  `target_name` varchar(200) DEFAULT NULL COMMENT '目标对象名称',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作人姓名',
  `old_values` longtext COMMENT '变更前值（JSON格式）',
  `new_values` longtext COMMENT '变更后值（JSON格式）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `dealer_id` bigint DEFAULT NULL COMMENT '门店ID',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `updated_by` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_module` (`operation_module`),
  KEY `idx_target_type_id` (`target_type`,`target_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_dealer_id` (`dealer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志表';


-- ----------------------------
-- Table structure for tt_order
-- ----------------------------
DROP TABLE IF EXISTS `tt_order`;
CREATE TABLE `tt_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单唯一编号',
  `customer_id` bigint DEFAULT NULL COMMENT '购车人ID，关联客户表',
  `salesman_id` bigint DEFAULT NULL COMMENT '销售顾问ID，关联用户表',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID，关联门店表',
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人姓名',
  `customer_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人手机号',
  `customer_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人类别',
  `id_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人证件类别',
  `id_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人证件号',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人邮箱',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人地址',
  `state` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人所在州',
  `city` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人所在城市',
  `zip_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '购车人邮编',
  `model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车型',
  `variant` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置',
  `color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '颜色',
  `vin` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车架号',
  `payment_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式: 全款, 贷款',
  `deposit_amount` decimal(12,2) DEFAULT NULL COMMENT '应付定金金额',
  `loan_amount` decimal(12,2) DEFAULT NULL COMMENT '应付贷款金额',
  `loan_term` int DEFAULT NULL COMMENT '贷款期数 (月)',
  `vehicle_price` decimal(12,2) DEFAULT NULL COMMENT '车辆销售小计',
  `number_plates_fee` decimal(12,2) DEFAULT NULL COMMENT '车牌费',
  `total_invoice_price` decimal(12,2) DEFAULT NULL COMMENT '整车开票价 (快照)',
  `remaining_receivable_amount` decimal(12,2) DEFAULT NULL COMMENT '剩余应收 (快照)',
  `order_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单状态',
  `payment_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单支付状态',
  `approval_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单审核状态',
  `loan_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '贷款审核状态',
  `insurance_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投保状态',
  `jpj_registration_status` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JPJ车辆注册状态',
  `allocation_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'unallocated' COMMENT '配车状态：wait 等待 unallocated-未配车，allocated-已配车，cancelled-已取消',
  `allocation_time` timestamp NULL DEFAULT NULL COMMENT '配车时间，配车成功时记录',
  `allocated_vehicle_id` bigint DEFAULT NULL COMMENT '已配车辆ID，关联tt_vehicle_inventory.id',
  `invoice_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开票类型: personal, company',
  `invoice_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开票名称',
  `invoice_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开票电话',
  `invoice_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开票地址',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `optional_parts_amount` decimal(12,2) DEFAULT '0.00' COMMENT '选装配件总金额',
  `insurance_amount` decimal(12,2) DEFAULT '0.00' COMMENT '保险金额',
  `promotion_discount_amount` decimal(12,2) DEFAULT '0.00' COMMENT '订单优惠金额',
  `other_fees_amount` decimal(12,2) DEFAULT '0.00' COMMENT '其他附加费用',
  `paid_customer_amount` decimal(12,2) DEFAULT '0.00' COMMENT '客户已支付金额（快照）',
  `final_payment_amount` decimal(12,2) DEFAULT '0.00' COMMENT '客户尾款金额（快照）',
  `order_total_amount` decimal(12,2) DEFAULT '0.00' COMMENT '订单总金额（未扣减优惠）',
  `order_net_amount` decimal(12,2) DEFAULT '0.00' COMMENT '订单应收净额（已扣减优惠）',
  `otd_price` decimal(12,2) DEFAULT '0.00' COMMENT 'OTD价格（全包上路价）',
  `order_paid_amount` decimal(12,2) DEFAULT '0.00' COMMENT '订单支付总额（定金 + 贷款 + 尾款）',
  `ort_total_amount` decimal(12,2) DEFAULT '0.00' COMMENT 'ORT费用总额（注册/运输/手续费等）',
  `invoice_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发票号码',
  `invoice_date` datetime DEFAULT NULL COMMENT '开票时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_salesman_id` (`salesman_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_allocation_query` (`allocation_status`,`order_status`,`created_at`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_allocation_time` (`allocation_time`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售订单主表';

-- ----------------------------
-- Table structure for tt_order_accessory
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_accessory`;
CREATE TABLE `tt_order_accessory` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID, 关联 tt_sales_order.id',
  `category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配件类别',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配件名称',
  `price` decimal(10,2) NOT NULL COMMENT '配件单价',
  `quantity` int NOT NULL COMMENT '数量',
  `total` decimal(12,2) NOT NULL COMMENT '总价',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单选配件表';

-- ----------------------------
-- Table structure for tt_order_allocation_record
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_allocation_record`;
CREATE TABLE `tt_order_allocation_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID，关联tt_order.id',
  `vehicle_id` bigint DEFAULT NULL COMMENT '车辆ID，关联tt_vehicle_inventory.id，取消配车时可为空',
  `operation_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：allocate-配车，cancel_allocate-取消配车，view_detail-查看详情',
  `operator_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人工号',
  `operator_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人姓名',
  `operation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `process_result` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理结果：success-成功，failed-失败',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '操作备注说明',
  `operation_details` json DEFAULT NULL COMMENT '操作详情JSON，记录具体操作参数和结果',
  `is_system_operation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统操作：0-人工操作，1-系统自动操作',
  `vehicle_vin` varchar(17) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车辆VIN码快照',
  `vehicle_model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车辆型号快照',
  `vehicle_variant` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车辆配置快照',
  `vehicle_color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车辆颜色快照',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_time` (`order_id`,`operation_time` DESC),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_code` (`operator_code`),
  KEY `idx_process_result` (`process_result`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单配车记录表';

-- ----------------------------
-- Table structure for tt_order_approval
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_approval`;
CREATE TABLE `tt_order_approval` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `approval_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批单号，唯一标识',
  `approval_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '审批类型：1-取消订单审批，2-修改订单审批',
  `order_id` bigint NOT NULL COMMENT '关联的订单ID，关联tt_order.id',
  `order_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号，冗余字段方便查询',
  `submitter_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交人姓名',
  `submit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请原因（取消原因或修改原因）',
  `approval_result` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批结果：1-通过，2-驳回 (待审批时为NULL)',
  `approval_time` timestamp NULL DEFAULT NULL COMMENT '审批完成时间 (待审批时为NULL)',
  `comment` text COLLATE utf8mb4_unicode_ci COMMENT '审核备注（选填）',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `approval_user_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批人姓名',
  `dealer_id` bigint NOT NULL COMMENT '门店ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_approval_no` (`approval_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_submitter_name` (`submitter_name`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_approval_type_result` (`approval_type`,`approval_result`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单审批表';

-- ----------------------------
-- Table structure for tt_order_approval_detail
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_approval_detail`;
CREATE TABLE `tt_order_approval_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `approval_id` bigint NOT NULL COMMENT '关联的审批单ID，关联tt_order_approval.id',
  `detail_type` tinyint(1) NOT NULL COMMENT '明细类型：1-车型，2-外观颜色，3-订单金额等',
  `detail_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '明细名称，如：车型、外观颜色、订单金额',
  `original_data` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更前的原始数据',
  `changed_data` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更后的数据',
  `operator_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人姓名',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_approval_id` (`approval_id`),
  KEY `idx_detail_type` (`detail_type`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单审批明细表';

-- ----------------------------
-- Table structure for tt_order_change_record
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_change_record`;
CREATE TABLE `tt_order_change_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID, 关联 tt_sales_order.id',
  `original_content` text COLLATE utf8mb4_unicode_ci COMMENT '原始内容',
  `changed_content` text COLLATE utf8mb4_unicode_ci COMMENT '变更后内容',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单变更记录表';

-- ----------------------------
-- Table structure for tt_order_otr_fee
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_otr_fee`;
CREATE TABLE `tt_order_otr_fee` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID, 关联 tt_sales_order.id',
  `invoice_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '票据单号',
  `item` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收费项目',
  `price` decimal(10,2) NOT NULL COMMENT '收费价格',
  `effective_date` date DEFAULT NULL COMMENT '生效日期',
  `expiry_date` date DEFAULT NULL COMMENT '到期日期',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单OTR费用表';

-- ----------------------------
-- Table structure for tt_order_policy
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_policy`;
CREATE TABLE `tt_order_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID, 关联 tt_sales_order.id',
  `policy_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保单号',
  `insurance_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保险类型',
  `insurance_company` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保险公司',
  `price` decimal(12,2) DEFAULT NULL COMMENT '保险价格',
  `effective_date` date DEFAULT NULL COMMENT '生效日期',
  `expiry_date` date DEFAULT NULL COMMENT '到期日期',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_policy_number` (`policy_number`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单保单表';

-- ----------------------------
-- Table structure for tt_order_right
-- ----------------------------
DROP TABLE IF EXISTS `tt_order_right`;
CREATE TABLE `tt_order_right` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID, 关联 tt_sales_order.id',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权益代码, 关联权益主数据',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权益名称',
  `mode` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权益模式',
  `discount_price` decimal(12,2) NOT NULL COMMENT '权益优惠价格',
  `effective_date` date DEFAULT NULL COMMENT '权益生效日期',
  `expiry_date` date DEFAULT NULL COMMENT '权益终止日期',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单权益表';

-- ----------------------------
-- Table structure for tt_payment_record
-- ----------------------------
DROP TABLE IF EXISTS `tt_payment_record`;
CREATE TABLE `tt_payment_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `payment_record_number` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收退款单号，格式：PAY+门店代码+年月日+序号',
  `order_id` bigint NOT NULL COMMENT '关联订单ID，关联tt_order.id',
  `business_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务类型：收款/退款',
  `transaction_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '流水号，全局唯一',
  `channel` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道：APP/银行卡/转账',
  `amount` decimal(12,2) NOT NULL COMMENT '金额，单位：元',
  `payment_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收款类型：Book Fee/贷款/尾款',
  `arrival_time` date NOT NULL COMMENT '到账时间',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息',
  `original_record_id` bigint DEFAULT NULL COMMENT '原始记录ID，冲正记录关联原始记录',
  `data_source` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据来源：手动录入/APP推送/手动补录',
  `is_manual_supplement` tinyint(1) DEFAULT '0' COMMENT '是否手动补录：0-否，1-是',
  `is_reversible` tinyint(1) DEFAULT '1' COMMENT '是否可冲正：0-不可冲正，1-可冲正',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_record_number` (`payment_record_number`),
  UNIQUE KEY `uk_transaction_number` (`transaction_number`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_business_type` (`order_id`,`business_type`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_channel` (`channel`),
  KEY `idx_arrival_time` (`arrival_time`),
  KEY `idx_original_record_id` (`original_record_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `tt_payment_record_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `tt_order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收退款记录表';

-- ----------------------------
-- Table structure for tt_prospect_change_log
-- ----------------------------
DROP TABLE IF EXISTS `tt_prospect_change_log`;
CREATE TABLE `tt_prospect_change_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，记录ID',
  `store_prospect_id` bigint NOT NULL COMMENT '门店潜客ID，关联tt_store_prospect.id',
  `change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `change_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据变更类型，例如：属性更新、状态流转、归属变更等',
  `operator_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人ID（销售顾问或销售经理）',
  `original_value` text COLLATE utf8mb4_unicode_ci COMMENT '变更前的原始信息，多字段时可逗号或分号分割',
  `new_value` text COLLATE utf8mb4_unicode_ci COMMENT '变更后的新信息，多字段时可逗号或分号分割',
  `change_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变更原因',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_store_prospect_id` (`store_prospect_id`),
  KEY `idx_change_time` (`change_time`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='潜客变更日志表';

-- ----------------------------
-- Table structure for tt_prospect_lead
-- ----------------------------
DROP TABLE IF EXISTS `tt_prospect_lead`;
CREATE TABLE `tt_prospect_lead` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，线索ID / 客户全局ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号',
  `id_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证件类型',
  `id_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户证件号码',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户邮箱',
  `registration_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '线索创建时间',
  `source_channel` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '线索来源渠道',
  `registration_area` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '线索注册区域',
  `current_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '线索的当前处理状态，如：未处理、已关联门店、无效线索',
  `associated_store_count` int DEFAULT '0' COMMENT '标识该线索关联的门店潜客记录数量',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  KEY `idx_source_channel` (`source_channel`),
  KEY `idx_current_status` (`current_status`),
  KEY `idx_registration_time` (`registration_time`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='潜客线索表';

-- ----------------------------
-- Table structure for tt_registration_fee_detail
-- ----------------------------
DROP TABLE IF EXISTS `tt_registration_fee_detail`;
CREATE TABLE `tt_registration_fee_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `registration_id` bigint NOT NULL COMMENT '车辆登记ID，关联tt_vehicle_registration.id',
  `fee_type_display` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '费用类型名称（显示用）',
  `amount` decimal(12,2) NOT NULL COMMENT '费用金额',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_registration_id` (`registration_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3040 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登记费用明细表';

-- ----------------------------
-- Table structure for tt_registration_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `tt_registration_operation_log`;
CREATE TABLE `tt_registration_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `registration_id` bigint NOT NULL COMMENT '车辆登记ID，关联tt_vehicle_registration.id',
  `operation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `operation_source` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作来源：DMS/APP/SYSTEM',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID，关联tt_user.id',
  `operator_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人姓名',
  `ip_address` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作IP地址',
  `result` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作结果：成功/失败',
  `before_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变更前状态',
  `after_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变更后状态',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '操作备注',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_registration_id` (`registration_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4039 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登记操作日志表';



-- ----------------------------
-- Table structure for tt_store_prospect
-- ----------------------------
DROP TABLE IF EXISTS `tt_store_prospect`;
CREATE TABLE `tt_store_prospect` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，门店潜客ID',
  `global_customer_id` bigint NOT NULL COMMENT '客户全局ID，关联tt_prospect_lead.id',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '潜客名称',
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '潜客手机号',
  `id_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证件类别',
  `id_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证件号',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `region` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `lead_association_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '该线索被关联到此门店的时间',
  `association_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联原因',
  `assignment_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '该潜客被分配给当前销售顾问的时间',
  `current_sales_advisor_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '当前负责销售顾问ID',
  `current_intent_level` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '门店潜客当前的意向级别，如：H,A,B,C,F,O',
  `last_follow_up_time` timestamp NULL DEFAULT NULL COMMENT '最近一次跟进该门店潜客的时间',
  `next_follow_up_time` timestamp NULL DEFAULT NULL COMMENT '系统根据意向级别和跟进时间计算出的下次需跟进时间',
  `prospect_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '潜客状态，如：跟进中,战败审批中,已战败,已成交',
  `intent_model` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '意向Model',
  `intent_variant` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '意向Variant',
  `intent_color` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '意向Color',
  `source_channel` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源渠道 (冗余自线索)',
  `big_region` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '大区，供区域经理查询区分',
  `small_region` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '小区，供区域经理查询区分',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`,`current_sales_advisor_id`) USING BTREE,
  UNIQUE KEY `uk_global_customer_store` (`global_customer_id`,`store_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_current_sales_advisor_id` (`current_sales_advisor_id`),
  KEY `idx_current_intent_level` (`current_intent_level`),
  KEY `idx_next_follow_up_time` (`next_follow_up_time`),
  KEY `idx_prospect_status` (`prospect_status`),
  KEY `idx_source_channel` (`source_channel`),
  KEY `idx_big_region` (`big_region`),
  KEY `idx_small_region` (`small_region`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='门店潜客信息表';



-- ----------------------------
-- Table structure for tt_test_drive_order
-- ----------------------------
DROP TABLE IF EXISTS `tt_test_drive_order`;
CREATE TABLE `tt_test_drive_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `test_drive_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '试驾单号',
  `customer_id` bigint NOT NULL COMMENT '客户ID，关联外部系统客户表',
  `sales_consultant_id` bigint NOT NULL COMMENT '销售顾问ID，关联外部系统销售顾问表',
  `sales_consultant_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销售顾问姓名',
  `store_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店ID，关联外部系统门店表',
  `store_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店名称',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '试驾开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '试驾结束时间',
  `start_mileage` decimal(10,2) DEFAULT NULL COMMENT '起始里程(km)',
  `end_mileage` decimal(10,2) DEFAULT NULL COMMENT '结束里程(km)',
  `model` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车型',
  `variant` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车型配置',
  `plate_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车牌号',
  `vin` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车架号',
  `feedback` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注反馈',
  `driver_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '试驾人姓名',
  `driver_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '试驾人手机号',
  `driver_id_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '证件类型：1-身份证，2-护照，3-军官证，4-其他',
  `driver_id_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '证件号码',
  `driver_license_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '驾驶证号',
  `driver_license_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '驾驶证类型',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_test_drive_no` (`test_drive_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_sales_consultant_id` (`sales_consultant_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_model` (`model`),
  KEY `idx_driver_id_number` (`driver_id_number`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试驾登记单表';



-- ----------------------------
-- Table structure for tt_vehicle_inventory
-- ----------------------------
DROP TABLE IF EXISTS `tt_vehicle_inventory`;
CREATE TABLE `tt_vehicle_inventory` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID，关联tt_order.id',
  `vin` varchar(17) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆识别代号，全球唯一标识',
  `factory_order_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工厂生产订单号',
  `dealer_id` bigint NOT NULL COMMENT '门店ID，区分不同门店的车辆库存',
  `model` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车型系列',
  `variant` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车型配置版本',
  `color` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆颜色',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID，关联基础数据',
  `stock_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '库存状态：inStock-在库，allocated-配车，inTransit-在途，transferred-调拨',
  `lock_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '锁定状态：locked-已锁定，unlocked-未锁定',
  `invoice_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开票状态：invoiced-已开票，notInvoiced-未开票',
  `invoice_date` timestamp NULL DEFAULT NULL COMMENT '开票时间',
  `delivery_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交车状态：delivered-已交车，notDelivered-未交车',
  `delivery_date` timestamp NULL DEFAULT NULL COMMENT '交车时间',
  `storage_date` timestamp NOT NULL COMMENT '入库时间',
  `production_date` timestamp NOT NULL COMMENT '生产时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '-1' COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '-1' COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vin` (`vin`),
  KEY `idx_dealer_id` (`dealer_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_factory_order_no` (`factory_order_no`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_storage_date` (`storage_date`),
  KEY `idx_dealer_storage` (`dealer_id`,`storage_date`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_available_vehicles` (`stock_status`,`lock_status`,`model`,`variant`,`color`),
  KEY `idx_allocation_status` (`stock_status`,`lock_status`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆库存表';

-- ----------------------------
-- Table structure for tt_vehicle_registration
-- ----------------------------
DROP TABLE IF EXISTS `tt_vehicle_registration`;
CREATE TABLE `tt_vehicle_registration` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单ID，关联tt_order.id',
  `order_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号，冗余字段',
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名，冗余字段',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户手机号，冗余字段',
  `vin` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆识别代码，冗余字段',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登记状态：待登记/登记中/登记成功/登记失败',
  `push_count` int DEFAULT '0' COMMENT '推送次数',
  `last_push_time` timestamp NULL DEFAULT NULL COMMENT '最后推送时间',
  `completion_time` timestamp NULL DEFAULT NULL COMMENT '登记完成时间',
  `certificate_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记证书号',
  `registration_fee` decimal(12,2) DEFAULT NULL COMMENT '登记总费用',
  `failure_reason` text COLLATE utf8mb4_unicode_ci COMMENT '登记失败原因',
  `jpj_response_data` text COLLATE utf8mb4_unicode_ci COMMENT 'JPJ系统返回的原始数据',
  `operator_id` bigint DEFAULT NULL COMMENT '操作员ID，关联tt_user.id',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人ID',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_push_time` (`last_push_time`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_vin` (`vin`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆登记表';

SET FOREIGN_KEY_CHECKS = 1;
