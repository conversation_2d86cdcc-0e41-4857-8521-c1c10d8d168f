# 销售订单管理 API 文档 v1.0

## 接口基础信息

- **基础路径**: `/api/v1/sales`
- **认证方式**: Bearer Token
- **响应格式**: JSON
- **编码格式**: UTF-8

## 通用响应格式

```typescript
interface ApiResult<T> {
  success: boolean;      // 操作是否成功
  message: string;       // 响应消息
  code: string;          // 业务状态码，成功为"10000000"
  traceId: string;       // 链路追踪ID
  result: T;             // 响应数据
  timestamp: number;     // 时间戳
}

interface PageResponse<T> {
  records: T[];          // 数据列表
  total: number;         // 总记录数
  pageNum: number;       // 当前页码
  pageSize: number;      // 每页大小
  pages: number;         // 总页数
}
```

---

## 1. 查询订单列表

### 接口信息
- **接口名称**: 查询销售订单列表
- **请求方法**: `GET`
- **接口路径**: `/orders`
- **接口描述**: 根据多维度条件查询销售订单列表，支持分页

### 请求参数

| 参数名 | 类型 | 是否必须 | 默认值 | 说明 |
|:---|:---|:---|:---|:---|
| `orderNo` | `string` | `false` | - | 订单编号，支持模糊查询 |
| `customerName` | `string` | `false` | - | 购车人姓名，支持模糊查询 |
| `customerPhone` | `string` | `false` | - | 购车人手机号，支持模糊查询 |
| `customerType` | `string` | `false` | - | 购车人类别：`individual`-个人，`company`-公司 |
| `model` | `string` | `false` | - | 车型：`Myvi`、`Axia`、`Bezza`、`Aruz` |
| `variant` | `string` | `false` | - | 车款配置 |
| `orderStatus` | `string` | `false` | - | 订单状态：`pending`-待确认，`confirmed`-已确认，`completed`-已完成，`cancelled`-已取消 |
| `approvalStatus` | `string` | `false` | - | 审核状态：`pending`-待审核，`approved`-已通过，`rejected`-已驳回 |
| `paymentStatus` | `string` | `false` | - | 支付状态：`unpaid`-未付款，`partial_paid`-部分付款，`paid`-已付款 |
| `insuranceStatus` | `string` | `false` | - | 投保状态：`pending`-待投保，`insured`-已投保，`expired`-已过期 |
| `loanStatus` | `string` | `false` | - | 贷款状态：`pending`-待审批，`approved`-已批准，`rejected`-已拒绝 |
| `jpjRegistrationStatus` | `string` | `false` | - | JPJ注册状态：`pending`-待注册，`registered`-已注册，`expired`-已过期 |
| `createdAtStart` | `string` | `false` | - | 创建时间开始，格式：`YYYY-MM-DD HH:mm:ss` |
| `createdAtEnd` | `string` | `false` | - | 创建时间结束，格式：`YYYY-MM-DD HH:mm:ss` |
| `pageNum` | `number` | `false` | `1` | 当前页码，从1开始 |
| `pageSize` | `number` | `false` | `20` | 每页条数，最大100 |

### 响应示例

```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-12345",
  "result": {
    "records": [
      {
        "id": 1,
        "orderNo": "ORD202310270001",
        "customerId": 1001,
        "customerName": "张三",
        "customerPhone": "138****1234",
        "customerType": "individual",
        "salesmanId": 2001,
        "storeId": 3001,
        "model": "Myvi",
        "variant": "1.5 AV",
        "color": "Red",
        "vin": "MBBHA26E8JT123456",
        "paymentMethod": "loan",
        "depositAmount": 5000.00,
        "loanAmount": 45000.00,
        "loanTerm": 60,
        "vehiclePrice": 48000.00,
        "totalInvoicePrice": 50000.00,
        "orderStatus": "confirmed",
        "paymentStatus": "partial_paid",
        "approvalStatus": "approved",
        "loanStatus": "approved",
        "insuranceStatus": "pending",
        "jpjRegistrationStatus": "pending",
        "allocationStatus": "allocated",
        "allocationTime": "2023-10-27 14:30:00",
        "allocatedVehicleId": 4001,
        "optionalPartsAmount": 1200.00,
        "insuranceAmount": 800.00,
        "promotionDiscountAmount": 500.00,
        "orderTotalAmount": 50000.00,
        "orderNetAmount": 49500.00,
        "createdAt": "2023-10-27 10:30:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  },
  "timestamp": 1698393000000
}
```

---

## 2. 获取订单详情

### 接口信息
- **接口名称**: 获取销售订单详情
- **请求方法**: `GET`
- **接口路径**: `/orders/{orderNo}`
- **接口描述**: 根据订单号获取订单完整详情信息

### 路径参数

| 参数名 | 类型 | 是否必须 | 说明 |
|:---|:---|:---|:---|
| `orderNo` | `string` | `true` | 订单编号 |

### 响应示例

```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-12346",
  "result": {
    "id": 1,
    "orderNo": "ORD202310270001",
    "customerId": 1001,
    "customerName": "张三",
    "customerPhone": "138****1234",
    "customerType": "individual",
    "salesmanId": 2001,
    "storeId": 3001,
    "model": "Myvi",
    "variant": "1.5 AV",
    "color": "Red",
    "vin": "MBBHA26E8JT123456",
    "paymentMethod": "loan",
    "depositAmount": 5000.00,
    "loanAmount": 45000.00,
    "loanTerm": 60,
    "vehiclePrice": 48000.00,
    "numberPlatesFee": 500.00,
    "totalInvoicePrice": 50000.00,
    "remainingReceivableAmount": 44500.00,
    "orderStatus": "confirmed",
    "paymentStatus": "partial_paid",
    "approvalStatus": "approved",
    "loanStatus": "approved",
    "insuranceStatus": "pending",
    "jpjRegistrationStatus": "pending",
    "allocationStatus": "allocated",
    "allocationTime": "2023-10-27 14:30:00",
    "allocatedVehicleId": 4001,
    "optionalPartsAmount": 1200.00,
    "insuranceAmount": 800.00,
    "promotionDiscountAmount": 500.00,
    "otherFeesAmount": 300.00,
    "paidCustomerAmount": 5000.00,
    "finalPaymentAmount": 44500.00,
    "orderTotalAmount": 50000.00,
    "orderNetAmount": 49500.00,
    "otdPrice": 50800.00,
    "orderPaidAmount": 5000.00,
    "ortTotalAmount": 800.00,
    "invoiceNumber": "INV202310270001",
    "invoiceDate": "2023-10-27 16:00:00",
    "createdAt": "2023-10-27 10:30:00",
    "updatedAt": "2023-10-27 16:00:00",
    "optionalParts": [
      {
        "id": 1,
        "orderId": 1,
        "partCode": "ACC001",
        "partName": "真皮座椅",
        "unitPrice": 800.00,
        "quantity": 1,
        "totalPrice": 800.00,
        "createdAt": "2023-10-27 10:30:00"
      },
      {
        "id": 2,
        "orderId": 1,
        "partCode": "ACC002",
        "partName": "导航系统",
        "unitPrice": 400.00,
        "quantity": 1,
        "totalPrice": 400.00,
        "createdAt": "2023-10-27 10:30:00"
      }
    ]
  },
  "timestamp": 1698393000000
}
```

---

## 3. 查询可选配件列表

### 接口信息
- **接口名称**: 查询可选配件列表
- **请求方法**: `GET`
- **接口路径**: `/optional-parts`
- **接口描述**: 查询可选配件列表，用于权益选择弹窗

### 请求参数

| 参数名 | 类型 | 是否必须 | 默认值 | 说明 |
|:---|:---|:---|:---|:---|
| `partCode` | `string` | `false` | - | 配件代码，支持模糊查询 |
| `partName` | `string` | `false` | - | 配件名称，支持模糊查询 |
| `pageNum` | `number` | `false` | `1` | 当前页码，从1开始 |
| `pageSize` | `number` | `false` | `20` | 每页条数，最大100 |

### 响应示例

```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-12347",
  "result": {
    "records": [
      {
        "partCode": "ACC001",
        "partName": "真皮座椅",
        "unitPrice": 800.00,
        "description": "高级真皮座椅套装",
        "category": "interior",
        "isAvailable": true
      },
      {
        "partCode": "ACC002",
        "partName": "导航系统",
        "unitPrice": 400.00,
        "description": "GPS导航娱乐系统",
        "category": "electronics",
        "isAvailable": true
      },
      {
        "partCode": "ACC003",
        "partName": "行车记录仪",
        "unitPrice": 300.00,
        "description": "高清行车记录仪",
        "category": "electronics",
        "isAvailable": true
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 3
  },
  "timestamp": 1698393000000
}
```

---

## 4. 更新订单选装配件

### 接口信息
- **接口名称**: 更新订单选装配件
- **请求方法**: `PUT`
- **接口路径**: `/orders/{orderNo}/optional-parts`
- **接口描述**: 更新指定订单的选装配件信息

### 路径参数

| 参数名 | 类型 | 是否必须 | 说明 |
|:---|:---|:---|:---|
| `orderNo` | `string` | `true` | 订单编号 |

### 请求体

```json
{
  "optionalParts": [
    {
      "partCode": "ACC001",
      "partName": "真皮座椅",
      "unitPrice": 800.00,
      "quantity": 1,
      "totalPrice": 800.00
    },
    {
      "partCode": "ACC003",
      "partName": "行车记录仪",
      "unitPrice": 300.00,
      "quantity": 1,
      "totalPrice": 300.00
    }
  ]
}
```

### 请求体字段说明

| 字段名 | 类型 | 是否必须 | 说明 |
|:---|:---|:---|:---|
| `optionalParts` | `array` | `true` | 选装配件列表 |
| `optionalParts[].partCode` | `string` | `true` | 配件代码 |
| `optionalParts[].partName` | `string` | `true` | 配件名称 |
| `optionalParts[].unitPrice` | `number` | `true` | 单价 |
| `optionalParts[].quantity` | `number` | `true` | 数量 |
| `optionalParts[].totalPrice` | `number` | `true` | 小计金额 |

### 响应示例

```json
{
  "success": true,
  "message": "选装配件更新成功",
  "code": "10000000",
  "traceId": "trace-12348",
  "result": null,
  "timestamp": 1698393000000
}
```

---

## 5. 获取订单编辑信息

### 接口信息
- **接口名称**: 获取订单编辑信息
- **请求方法**: `GET`
- **接口路径**: `/orders/{orderNo}/edit`
- **接口描述**: 获取订单编辑页面所需的完整信息

### 路径参数

| 参数名 | 类型 | 是否必须 | 说明 |
|:---|:---|:---|:---|
| `orderNo` | `string` | `true` | 订单编号 |

### 响应示例

```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-12349",
  "result": {
    "id": 1,
    "orderNo": "ORD202310270001",
    "customerId": 1001,
    "customerName": "张三",
    "customerPhone": "138****1234",
    "customerType": "individual",
    "salesmanId": 2001,
    "storeId": 3001,
    "model": "Myvi",
    "variant": "1.5 AV",
    "color": "Red",
    "vin": "MBBHA26E8JT123456",
    "paymentMethod": "loan",
    "depositAmount": 5000.00,
    "loanAmount": 45000.00,
    "loanTerm": 60,
    "vehiclePrice": 48000.00,
    "numberPlatesFee": 500.00,
    "totalInvoicePrice": 50000.00,
    "remainingReceivableAmount": 44500.00,
    "orderStatus": "confirmed",
    "paymentStatus": "partial_paid",
    "approvalStatus": "approved",
    "loanStatus": "approved",
    "insuranceStatus": "pending",
    "jpjRegistrationStatus": "pending",
    "allocationStatus": "allocated",
    "allocationTime": "2023-10-27 14:30:00",
    "allocatedVehicleId": 4001,
    "optionalPartsAmount": 1200.00,
    "insuranceAmount": 800.00,
    "promotionDiscountAmount": 500.00,
    "otherFeesAmount": 300.00,
    "paidCustomerAmount": 5000.00,
    "finalPaymentAmount": 44500.00,
    "orderTotalAmount": 50000.00,
    "orderNetAmount": 49500.00,
    "otdPrice": 50800.00,
    "orderPaidAmount": 5000.00,
    "ortTotalAmount": 800.00,
    "invoiceNumber": "INV202310270001",
    "invoiceDate": "2023-10-27 16:00:00",
    "createdAt": "2023-10-27 10:30:00",
    "updatedAt": "2023-10-27 16:00:00",
    "optionalParts": [
      {
        "id": 1,
        "orderId": 1,
        "partCode": "ACC001",
        "partName": "真皮座椅",
        "unitPrice": 800.00,
        "quantity": 1,
        "totalPrice": 800.00,
        "createdAt": "2023-10-27 10:30:00"
      }
    ]
  },
  "timestamp": 1698393000000
}
```

---

## 错误码说明

| 错误码 | 说明 |
|:---|:---|
| `10000000` | 操作成功 |
| `40000001` | 参数错误 |
| `40000002` | 订单不存在 |
| `40000003` | 配件不存在 |
| `50000001` | 系统内部错误 |
| `50000002` | 数据库操作失败 |

## 注意事项

1. **分页参数**: `pageNum`从1开始，`pageSize`最大值为100
2. **时间格式**: 统一使用`YYYY-MM-DD HH:mm:ss`格式
3. **金额字段**: 统一使用`number`类型，保留2位小数
4. **状态枚举**: 所有状态字段都有固定的枚举值，请参考字段说明
5. **权限控制**: 所有接口都需要有效的Bearer Token
6. **数据一致性**: 更新选装配件时会自动重新计算订单总金额

## 版本历史

| 版本 | 日期 | 变更内容 |
|:---|:---|:---|
| v1.0 | 2024-01-15 | 初始版本，包含基础CRUD接口 |

## 联系方式

- **开发团队**: 前端开发组
- **维护人员**: API开发团队
- **更新频率**: 根据业务需求迭代更新