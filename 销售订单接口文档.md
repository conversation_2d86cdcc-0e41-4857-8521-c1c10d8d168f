# 销售订单模块接口文档

基于线框图交互动作和数据库结构设计的完整接口文档。

## 1. 销售订单列表页面接口

### 1.1 订单列表查询接口

**接口路径**: `GET /api/v1/orders`

**接口描述**: 根据筛选条件查询销售订单列表，支持MyBatis-Plus分页

**请求参数**: `OrderPageQuery`
```java
public class OrderPageQuery {
    // MyBatis-Plus分页参数
    private Long pageNum = 1L;            // 当前页码，从1开始，默认1
    private Long pageSize = 10L;          // 每页大小，默认10

    // 筛选条件
    private String customerName;           // 购车人姓名
    private String customerPhone;          // 购车人电话
    private String customerType;           // 购车人类型
    private String model;                  // 车型
    private String orderNo;               // 订单号
    private String orderStatus;           // 订单状态
    private String approvalStatus;        // 审批状态
    private String paymentStatus;         // 付款状态
    private String insuranceStatus;       // 保险状态
    private String loanStatus;            // 贷款审批状态
    private String jpjRegistrationStatus; // JPJ注册状态
    private Date createTimeStart;         // 创建时间开始
    private Date createTimeEnd;           // 创建时间结束
    private String ordererName;           // 订购人姓名
    private String ordererPhone;          // 订购人电话
}
```

**响应数据**: `Result<IPage<OrderListVO>>`
```java
// IPage响应格式
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "result": {
    "records": [...],        // 订单列表数据
    "total": 100,           // 总记录数
    "size": 10,             // 每页大小
    "current": 1,           // 当前页码
    "pages": 10             // 总页数
  }
}
```
```java
public class OrderListVO {
    private Long id;                      // 序号
    private String orderNo;               // 订单号
    private Date createdAt;               // 创建时间
    private String ordererName;           // 订购人姓名
    private String ordererPhone;          // 订购人电话
    private String customerName;          // 购车人姓名
    private String customerPhone;         // 购车人电话
    private String customerType;          // 购车人类型
    private String model;                 // 车型
    private String variant;               // 车款
    private String color;                 // 颜色
    private String vin;                   // 车架号
    private String paymentMethod;         // 支付方式
    private String orderStatus;           // 订单状态
    private String approvalStatus;        // 审批状态
    private String paymentStatus;         // 付款状态
    private String insuranceStatus;       // 保险状态
    private String jpjRegistrationStatus; // JPJ注册状态
    private BigDecimal orderTotalAmount;  // 订单总额
}
```

## 2. 销售订单详情页面接口

### 2.1 订单详情查询接口

**接口路径**: `GET /api/v1/orders/{orderNo}`

**接口描述**: 根据订单号查询订单详细信息，包含订单变更记录

**路径参数**:
- `orderNo`: 订单号

**响应数据**: `Result<OrderDetailVO>`
```java
public class OrderDetailVO {
    // 订单概要信息
    private String orderNo;               // 订单号
    private Date createdAt;               // 创建时间
    private String orderStatus;           // 订单状态
    private String approvalStatus;        // 审批状态
    private String paymentStatus;         // 付款状态
    private String insuranceStatus;       // 保险状态
    private String jpjRegistrationStatus; // JPJ注册状态
    
    // 客户信息
    private String ordererName;           // 订购人姓名
    private String ordererPhone;          // 订购人电话
    private String customerName;          // 购车人姓名
    private String customerPhone;         // 购车人电话
    private String idType;                // 购车人证件类型
    private String idNumber;              // 购车人证件号码
    private String email;                 // 购车人邮箱
    private String address;               // 购车人地址
    private String state;                 // 购车人州属
    private String city;                  // 购车人城市
    private String zipCode;               // 购车人邮编
    private String customerType;          // 购车人类型
    
    // 购车门店信息
    private String region;                // 区域
    private String storeCity;             // 城市
    private String storeName;             // 门店名称
    private String salesConsultant;       // 销售顾问
    
    // 购车信息 - 车辆信息
    private String model;                 // 车型
    private String variant;               // 车款
    private String color;                 // 颜色
    private BigDecimal vehiclePrice;      // 销售小计
    private BigDecimal numberPlatesFee;   // 车牌费
    private String vin;                   // 车架号(VIN)
    private List<OrderAccessoryVO> accessories; // 选配件信息
    
    // 购车信息 - 开票信息
    private String invoiceType;           // 开票类型
    private String invoiceName;           // 抬头名称
    private String invoicePhone;          // 联系电话
    private String invoiceAddress;        // 开票地址
    
    // 购车信息 - 权益信息
    private List<OrderRightVO> rights;    // 权益列表
    
    // 购车信息 - 支付信息
    private String paymentMethod;         // 支付方式
    private String loanStatus;            // 贷款状态
    private BigDecimal depositAmount;     // 定金金额
    private BigDecimal loanAmount;        // 贷款金额
    private BigDecimal finalPaymentAmount; // 尾款金额
    
    // 购车信息 - 保险信息
    private List<OrderPolicyVO> policies; // 保险列表
    private String insuranceRemarks;      // 备注
    
    // 购车信息 - OTR费用信息
    private List<OrderOtrFeeVO> otrFees;  // OTR费用列表
    
    // 订单变更记录
    private List<OrderChangeRecordVO> changeRecords; // 变更记录
    
    // 金额汇总
    private BigDecimal orderTotalAmount;  // 订单总金额
    private BigDecimal remainingReceivableAmount; // 剩余应收
}
```

<!-- 订单变更记录已包含在订单详情查询接口中，不需要单独提供接口 -->

## 3. 销售订单编辑页面接口

### 3.1 订单编辑信息查询接口

**接口路径**: `GET /api/v1/orders/{orderNo}/edit`

**接口描述**: 获取订单编辑页面所需的数据，区分只读和可编辑字段

**路径参数**:
- `orderNo`: 订单号

**响应数据**: `Result<OrderEditVO>`
```java
public class OrderEditVO {
    // 只读信息 - 订单概要
    private String orderNo;               // 订单号
    private Date createdAt;               // 创建时间
    
    // 只读信息 - 客户信息
    private String ordererName;           // 订购人姓名
    private String ordererPhone;          // 订购人电话
    private String customerName;          // 购车人姓名
    private String customerPhone;         // 购车人电话
    private String idType;                // 购车人证件类型
    private String idNumber;              // 购车人证件号码
    private String email;                 // 购车人邮箱
    private String address;               // 购车人地址
    private String state;                 // 购车人州属
    private String city;                  // 购车人城市
    private String zipCode;               // 购车人邮编
    private String customerType;          // 购车人类型
    
    // 只读信息 - 购车门店信息
    private String region;                // 区域
    private String storeCity;             // 城市
    private String storeName;             // 门店名称
    private String salesConsultant;       // 销售顾问
    
    // 可编辑信息 - 车辆信息
    private String model;                 // 车型(只读)
    private String variant;               // 车款(只读)
    private String color;                 // 颜色(可编辑)
    private BigDecimal vehiclePrice;      // 销售小计(只读)
    private BigDecimal numberPlatesFee;   // 车牌费(只读)
    private String vin;                   // 车架号(只读)
    private List<OrderAccessoryVO> accessories; // 选配件信息(只读)
    
    // 只读信息 - 开票信息
    private String invoiceType;           // 开票类型
    private String invoiceName;           // 抬头名称
    private String invoicePhone;          // 联系电话
    private String invoiceAddress;        // 开票地址
    
    // 可编辑信息 - 权益信息
    private List<OrderRightVO> rights;    // 权益列表
    
    // 可编辑信息 - 支付信息
    private String paymentMethod;         // 支付方式
    private String loanStatus;            // 贷款审批状态
    private BigDecimal depositAmount;     // 定金金额(只读)
    private BigDecimal loanAmount;        // 贷款金额(可编辑)
    private Integer loanTerm;             // 贷款期数(可编辑)
    private BigDecimal finalPaymentAmount; // 尾款金额(只读)
    
    // 可编辑信息 - 保险信息
    private List<OrderPolicyVO> policies; // 保险列表(只读)
    private String insuranceRemarks;      // 保险备注(可编辑)
    
    // 只读信息 - OTR费用
    private List<OrderOtrFeeVO> otrFees;  // OTR费用列表
    
    // 金额汇总
    private BigDecimal orderTotalAmount;  // 订单总金额
    private BigDecimal remainingReceivableAmount; // 剩余应收
}
```

### 3.2 订单更新接口

**接口路径**: `PUT /api/v1/orders/{orderNo}`

**接口描述**: 更新订单信息，包括权益的添加和移除，前端计算金额后端校验

**路径参数**:
- `orderNo`: 订单号

**请求参数**: `OrderUpdateForm`
```java
public class OrderUpdateForm {
    private String color;                 // 颜色
    private String paymentMethod;         // 支付方式
    private String loanStatus;            // 贷款审批状态
    private BigDecimal loanAmount;        // 贷款金额
    private Integer loanTerm;             // 贷款期数
    private String insuranceRemarks;      // 保险备注
    private List<Long> rightIds;          // 权益ID列表（完整列表，用于添加和移除）

    // 前端计算的金额，后端进行校验
    private BigDecimal orderTotalAmount;  // 订单总金额
    private BigDecimal orderNetAmount;    // 订单应收净额
    private BigDecimal remainingReceivableAmount; // 剩余应收
    private BigDecimal rightsDiscountAmount; // 权益优惠总金额
}
```

**响应数据**: `Result<Void>`

## 4. 权益选择弹窗接口

### 4.1 可选权益列表查询接口

**接口路径**: `GET /api/v1/rights/available`

**接口描述**: 查询可选择的权益列表，支持搜索，不使用分页

**请求参数**: `RightListQuery`
```java
public class RightListQuery {
    // 搜索条件
    private String code;                  // 权益代码
    private String name;                  // 权益名称
}
```

**响应数据**: `Result<List<RightVO>>`
```java
public class RightVO {
    private Long id;                      // 权益ID
    private String code;                  // 权益代码
    private String name;                  // 权益名称
    private String mode;                  // 权益方式
    private BigDecimal discountPrice;     // 优惠金额
    private Date effectiveDate;           // 生效日期
    private Date expiryDate;              // 失效日期
}
```

<!-- 权益的添加和移除统一在订单更新接口中处理，不单独提供接口 -->

## 5. 通用VO对象定义

### 5.1 订单选配件VO
```java
public class OrderAccessoryVO {
    private String category;              // 配件类别
    private String name;                  // 配件名称
    private BigDecimal price;             // 单价
    private Integer quantity;             // 数量
    private BigDecimal total;             // 总价
}
```

### 5.2 订单权益VO
```java
public class OrderRightVO {
    private String code;                  // 权益代码
    private String name;                  // 权益名称
    private String mode;                  // 权益方式
    private BigDecimal discountPrice;     // 优惠金额
    private Date effectiveDate;           // 生效日期
    private Date expiryDate;              // 失效日期
}
```

### 5.3 订单保单VO
```java
public class OrderPolicyVO {
    private String policyNumber;          // 保单号
    private String insuranceType;         // 保险类型
    private String insuranceCompany;      // 保险公司
    private Date effectiveDate;           // 生效日期
    private Date expiryDate;              // 失效日期
    private BigDecimal price;             // 保费
}
```

### 5.4 订单OTR费用VO
```java
public class OrderOtrFeeVO {
    private String invoiceNumber;         // 票号
    private String item;                  // 费用项目
    private BigDecimal price;             // 费用
    private Date effectiveDate;           // 生效日期
    private Date expiryDate;              // 失效日期
}
```

## 6. 数据字典接口

数据字典使用系统公共接口，遵循《数据字典规范》文档。前端使用组合式函数API：

```typescript
// 单个字典使用
const {
  options,           // 字典选项列表
  getNameByCode,     // 根据code获取name
  getCodeByName,     // 根据name获取code
  loading            // 加载状态
} = useDictionary(DICTIONARY_TYPES.ORDER_STATUS);

// 批量字典使用
const {
  getOptions,        // 获取指定类型的选项
  getNameByCode,     // 根据类型和code获取name
  loading            // 加载状态
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.BUYER_TYPE
]);
```

### 6.1 相关字典类型

```typescript
// 业务状态字典
DICTIONARY_TYPES.ORDER_STATUS           // 订单状态
DICTIONARY_TYPES.APPROVAL_STATUS        // 审批状态
DICTIONARY_TYPES.PAYMENT_STATUS         // 付款状态
DICTIONARY_TYPES.INSURANCE_STATUS       // 保险状态
DICTIONARY_TYPES.LOAN_STATUS            // 贷款审批状态
DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS // JPJ注册状态

// 主数据字典
DICTIONARY_TYPES.VEHICLE_MODEL          // 车型
DICTIONARY_TYPES.VEHICLE_VARIANT        // 车款
DICTIONARY_TYPES.VEHICLE_COLOR          // 车辆颜色
DICTIONARY_TYPES.CUSTOMER_TYPE          // 购车人类型
DICTIONARY_TYPES.PAYMENT_METHOD         // 支付方式
DICTIONARY_TYPES.LOAN_TERM              // 贷款期数
```

## 7. 页面交互动作接口

### 7.1 列表页面交互动作

#### 7.1.1 重置筛选条件
- **前端动作**: 点击"重置"按钮
- **处理方式**: 前端清空筛选表单，无需调用后端接口

#### 7.1.2 查询订单列表
- **前端动作**: 点击"查询(S)"按钮
- **接口调用**: `GET /api/v1/orders` (已定义)

#### 7.1.3 查看订单详情
- **前端动作**: 点击表格行的"查"按钮
- **接口调用**: `GET /api/v1/orders/{orderNo}` (已定义)
- **页面跳转**: 跳转到订单详情页面

#### 7.1.4 编辑订单
- **前端动作**: 点击表格行的"编"按钮
- **接口调用**: `GET /api/v1/orders/{orderNo}/edit` (已定义)
- **页面跳转**: 跳转到订单编辑页面

#### 7.1.5 分页操作
- **前端动作**: 点击页码或跳转页面
- **接口调用**: `GET /api/v1/orders` 带分页参数（pageNum、pageSize）

### 7.2 详情页面交互动作

#### 7.2.1 Tab页切换
- **前端动作**: 点击不同Tab页（车辆信息、开票信息、权益信息、支付信息、保险信息、OTR费用）
- **处理方式**: 前端切换显示内容，无需调用后端接口

#### 7.2.2 返回列表
- **前端动作**: 点击"返回列表"按钮
- **处理方式**: 前端路由跳转，无需调用后端接口

### 7.3 编辑页面交互动作

#### 7.3.1 Tab页切换
- **前端动作**: 点击不同Tab页
- **处理方式**: 前端切换显示内容，无需调用后端接口

#### 7.3.2 添加权益
- **前端动作**: 点击"添加权益"按钮
- **接口调用**: `GET /api/v1/rights/available` (已定义)
- **弹窗显示**: 显示权益选择弹窗

#### 7.3.3 移除权益
- **前端动作**: 点击权益列表中的"移除"按钮
- **处理方式**: 前端从权益列表中移除，保存时统一调用订单更新接口

#### 7.3.4 支付方式切换
- **前端动作**: 切换支付方式下拉选择
- **处理方式**: 前端动态显示对应字段（全款/贷款模式），无需调用后端接口

#### 7.3.5 保存订单
- **前端动作**: 点击"保存"按钮
- **前端计算**: 计算各项金额（订单总金额、应收净额、剩余应收、权益优惠总金额）
- **接口调用**: `PUT /api/v1/orders/{orderNo}` (已定义，包含前端计算的金额用于后端校验)

#### 7.3.6 返回
- **前端动作**: 点击"返回"按钮
- **处理方式**: 前端路由跳转，无需调用后端接口

### 7.4 权益选择弹窗交互动作

#### 7.4.1 搜索权益
- **前端动作**: 输入搜索条件并点击"查询"按钮
- **接口调用**: `GET /api/v1/rights/available` 带搜索参数

#### 7.4.2 重置搜索
- **前端动作**: 点击"重置"按钮
- **处理方式**: 前端清空搜索表单，重新查询

#### 7.4.3 选择权益
- **前端动作**: 勾选可选权益列表中的复选框
- **处理方式**: 前端将选中项添加到已选列表，无需调用后端接口

#### 7.4.4 移除已选权益
- **前端动作**: 点击已选权益列表中的"移除"按钮
- **处理方式**: 前端从已选列表中移除，无需调用后端接口

#### 7.4.5 确认选择
- **前端动作**: 点击"确认选择"按钮
- **处理方式**: 前端将选中的权益添加到编辑页面权益列表，关闭弹窗
- **保存时机**: 权益变更在订单保存时统一提交到后端

#### 7.4.6 取消选择
- **前端动作**: 点击"取消"按钮或关闭按钮
- **处理方式**: 前端关闭弹窗，无需调用后端接口

<!-- 权益查询不使用分页，移除分页操作 -->

## 8. 前端金额计算说明

### 8.1 订单金额计算逻辑

**计算方式**: 前端根据订单信息实时计算各项金额，后端在保存时进行校验

**计算公式**:
```
// 订单总金额 = 车辆销售小计 + 车牌费 + 选配件总金额 + 保险金额 + ORT费用总额
orderTotalAmount = vehiclePrice + numberPlatesFee + optionalPartsAmount + insuranceAmount + ortTotalAmount

// 权益优惠总金额 = 所有权益的优惠金额之和
rightsDiscountAmount = sum(rights.discountPrice)

// 订单应收净额 = 订单总金额 - 权益优惠总金额
orderNetAmount = orderTotalAmount - rightsDiscountAmount

// 剩余应收 = 订单应收净额 - 已支付金额
remainingReceivableAmount = orderNetAmount - paidCustomerAmount
```

**前端实现**:
```typescript
// 实时计算示例
const calculateOrderAmount = () => {
  // 计算订单总金额
  orderTotalAmount.value =
    Number(vehiclePrice.value || 0) +
    Number(numberPlatesFee.value || 0) +
    Number(optionalPartsAmount.value || 0) +
    Number(insuranceAmount.value || 0) +
    Number(ortTotalAmount.value || 0);

  // 计算权益优惠总金额
  rightsDiscountAmount.value = selectedRights.value.reduce(
    (sum, right) => sum + Number(right.discountPrice || 0), 0
  );

  // 计算订单应收净额
  orderNetAmount.value = orderTotalAmount.value - rightsDiscountAmount.value;

  // 计算剩余应收
  remainingReceivableAmount.value = orderNetAmount.value - Number(paidCustomerAmount.value || 0);
};
```

## 9. 接口响应码定义

| 响应码 | 说明 |
|--------|------|
| 10000000 | 成功 |
| 20000001 | 订单不存在 |
| 20000002 | 订单状态不允许编辑 |
| 20000003 | 权益不存在 |
| 20000004 | 权益已过期 |
| 20000005 | 权益不可重复添加 |
| 20000006 | 金额校验失败 |
| 30000001 | 参数校验失败 |
| 30000002 | 必填参数缺失 |
| 30000003 | 参数格式错误 |
| 50000001 | 系统内部错误 |
| 50000002 | 数据库操作失败 |

## 10. 接口调用时序说明

### 10.1 订单列表页面加载时序
1. 页面初始化 → 调用字典接口获取下拉选项数据
2. 页面加载 → 调用订单列表接口获取默认数据
3. 用户筛选 → 调用订单列表接口获取筛选结果
4. 用户分页 → 调用订单列表接口获取对应页数据

### 10.2 订单详情页面加载时序
1. 页面初始化 → 调用订单详情接口获取完整信息
2. Tab切换 → 前端展示对应Tab内容（无接口调用）

### 10.3 订单编辑页面加载时序
1. 页面初始化 → 调用订单编辑信息接口获取数据
2. 页面初始化 → 使用字典组合式函数获取下拉选项数据
3. 添加权益 → 调用可选权益列表接口
4. 确认权益 → 前端更新权益列表，实时计算金额
5. 保存订单 → 前端计算所有金额，调用订单更新接口（包含完整权益列表和计算金额）

### 10.4 权益选择弹窗时序
1. 弹窗打开 → 调用可选权益列表接口（不分页，返回所有可用权益）
2. 搜索权益 → 调用可选权益列表接口（带搜索参数）
3. 确认选择 → 前端更新编辑页面权益信息，实时计算金额
4. 弹窗关闭 → 无需额外接口调用

## 11. 数据库字段映射关系

### 11.1 订单主表字段映射 (tt_order)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 订单号 | order_no | varchar(50) | 订单唯一编号 |
| 创建时间 | created_at | timestamp | 创建时间 |
| 订购人姓名 | customer_name | varchar(100) | 购车人姓名 |
| 订购人电话 | customer_phone | varchar(50) | 购车人手机号 |
| 购车人姓名 | customer_name | varchar(100) | 购车人姓名 |
| 购车人电话 | customer_phone | varchar(50) | 购车人手机号 |
| 购车人类型 | customer_type | varchar(20) | 购车人类别 |
| 购车人证件类型 | id_type | varchar(20) | 购车人证件类别 |
| 购车人证件号码 | id_number | varchar(100) | 购车人证件号 |
| 购车人邮箱 | email | varchar(100) | 购车人邮箱 |
| 购车人地址 | address | varchar(255) | 购车人地址 |
| 购车人州属 | state | varchar(100) | 购车人所在州 |
| 购车人城市 | city | varchar(100) | 购车人所在城市 |
| 购车人邮编 | zip_code | varchar(20) | 购车人邮编 |
| 车型 | model | varchar(100) | 车型 |
| 车款 | variant | varchar(100) | 配置 |
| 颜色 | color | varchar(50) | 颜色 |
| 车架号 | vin | varchar(50) | 车架号 |
| 支付方式 | payment_method | varchar(20) | 支付方式: 全款, 贷款 |
| 定金金额 | deposit_amount | decimal(12,2) | 应付定金金额 |
| 贷款金额 | loan_amount | decimal(12,2) | 应付贷款金额 |
| 贷款期数 | loan_term | int | 贷款期数 (月) |
| 销售小计 | vehicle_price | decimal(12,2) | 车辆销售小计 |
| 车牌费 | number_plates_fee | decimal(12,2) | 车牌费 |
| 订单状态 | order_status | varchar(30) | 订单状态 |
| 付款状态 | payment_status | varchar(30) | 订单支付状态 |
| 审批状态 | approval_status | varchar(30) | 订单审核状态 |
| 贷款审批状态 | loan_status | varchar(30) | 贷款审核状态 |
| 保险状态 | insurance_status | varchar(30) | 投保状态 |
| JPJ注册状态 | jpj_registration_status | varchar(30) | JPJ车辆注册状态 |
| 开票类型 | invoice_type | varchar(20) | 开票类型: personal, company |
| 抬头名称 | invoice_name | varchar(255) | 开票名称 |
| 联系电话 | invoice_phone | varchar(50) | 开票电话 |
| 开票地址 | invoice_address | varchar(255) | 开票地址 |
| 订单总金额 | order_total_amount | decimal(12,2) | 订单总金额（未扣减优惠） |
| 剩余应收 | remaining_receivable_amount | decimal(12,2) | 剩余应收 (快照) |
| 选配件总金额 | optional_parts_amount | decimal(12,2) | 选装配件总金额 |
| 保险金额 | insurance_amount | decimal(12,2) | 保险金额 |
| 权益优惠金额 | promotion_discount_amount | decimal(12,2) | 订单优惠金额 |
| OTR费用总金额 | ort_total_amount | decimal(12,2) | ORT费用总额 |

### 11.2 订单选配件表字段映射 (tt_order_accessory)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 配件类别 | category | varchar(100) | 配件类别 |
| 配件名称 | name | varchar(255) | 配件名称 |
| 单价 | price | decimal(10,2) | 配件单价 |
| 数量 | quantity | int | 数量 |
| 总价 | total | decimal(12,2) | 总价 |

### 11.3 订单权益表字段映射 (tt_order_right)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 权益代码 | code | varchar(50) | 权益代码 |
| 权益名称 | name | varchar(255) | 权益名称 |
| 权益方式 | mode | varchar(50) | 权益模式 |
| 优惠金额 | discount_price | decimal(12,2) | 权益优惠价格 |
| 生效日期 | effective_date | date | 权益生效日期 |
| 失效日期 | expiry_date | date | 权益终止日期 |

### 11.4 订单保单表字段映射 (tt_order_policy)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 保单号 | policy_number | varchar(100) | 保单号 |
| 保险类型 | insurance_type | varchar(100) | 保险类型 |
| 保险公司 | insurance_company | varchar(255) | 保险公司 |
| 生效日期 | effective_date | date | 生效日期 |
| 失效日期 | expiry_date | date | 到期日期 |
| 保费 | price | decimal(12,2) | 保险价格 |

### 11.5 订单OTR费用表字段映射 (tt_order_otr_fee)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 票号 | invoice_number | varchar(100) | 票据单号 |
| 费用项目 | item | varchar(255) | 收费项目 |
| 费用 | price | decimal(10,2) | 收费价格 |
| 生效日期 | effective_date | date | 生效日期 |
| 失效日期 | expiry_date | date | 到期日期 |

### 11.6 订单变更记录表字段映射 (tt_order_change_record)

| 线框图字段 | 数据库字段 | 字段类型 | 说明 |
|-----------|-----------|----------|------|
| 操作时间 | created_at | timestamp | 操作时间 |
| 操作人 | created_by | varchar(36) | 操作人ID |
| 变更前内容 | original_content | text | 原始内容 |
| 变更后内容 | changed_content | text | 变更后内容 |

## 12. 接口安全和权限

### 12.1 接口认证
- 所有接口都需要在请求头中携带有效的JWT Token
- Token格式: `Authorization: Bearer {token}`

### 12.2 接口权限控制
- 订单查询权限: 销售顾问只能查看自己的订单，销售经理可查看门店所有订单
- 订单编辑权限: 只有订单创建人和销售经理可以编辑订单
- 权益管理权限: 需要权益管理权限才能添加/移除权益

### 12.3 数据权限过滤
- 根据用户角色和门店权限自动过滤数据
- 销售顾问: 只能操作自己负责的订单
- 销售经理: 可以操作本门店的所有订单
- 区域经理: 可以查看区域内所有门店的订单

## 13. 接口性能要求

### 13.1 响应时间要求
- 列表查询接口: ≤ 2秒
- 详情查询接口: ≤ 1秒
- 更新操作接口: ≤ 3秒
- 字典数据接口: ≤ 500ms

### 13.2 并发要求
- 支持100个并发用户同时操作
- 订单编辑操作需要支持乐观锁防止并发冲突

### 13.3 缓存策略
- 字典数据接口使用Redis缓存，缓存时间1小时
- 订单详情数据缓存5分钟
- 权益列表数据缓存30分钟

## 14. 接口监控和日志

### 14.1 接口调用日志
- 记录所有接口的调用时间、参数、响应时间、响应结果
- 记录用户操作轨迹，便于问题排查

### 14.2 业务操作日志
- 订单创建、修改、删除操作需要记录详细的业务日志
- 权益添加、移除操作需要记录操作人和操作时间
- 状态变更操作需要记录变更前后的状态

### 14.3 异常监控
- 接口异常自动告警
- 响应时间超时告警
- 数据库连接异常告警

## 15. 接口测试用例

### 15.1 订单列表查询测试用例

**测试场景1: 正常查询**
```
请求: GET /api/v1/orders?pageNum=1&pageSize=10
期望响应:
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "result": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

**测试场景2: 带筛选条件查询**
```
请求: GET /api/v1/orders?customerName=张三&orderStatus=confirmed&pageNum=1&pageSize=10
期望响应: 返回符合条件的订单列表（IPage格式）
```

**测试场景3: 无数据查询**
```
请求: GET /api/v1/orders?customerName=不存在的客户&pageNum=1&pageSize=10
期望响应:
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "result": {
    "records": [],
    "total": 0,
    "size": 10,
    "current": 1,
    "pages": 0
  }
}
```

### 15.2 订单详情查询测试用例

**测试场景1: 正常查询**
```
请求: GET /api/v1/orders/ORD001
期望响应: 返回完整的订单详情信息（包含变更记录）
```

**测试场景2: 订单不存在**
```
请求: GET /api/v1/orders/ORD999999
期望响应:
{
  "success": false,
  "code": "20000001",
  "message": "订单不存在"
}
```

### 15.3 订单更新测试用例

**测试场景1: 正常更新**
```
请求: PUT /api/v1/orders/ORD001
请求体: {
  "color": "Electric Blue",
  "paymentMethod": "loan",
  "loanAmount": 40000.00,
  "loanTerm": 60,
  "rightIds": [1, 2],
  "orderTotalAmount": 52000.00,
  "orderNetAmount": 51800.00,
  "remainingReceivableAmount": 11000.00,
  "rightsDiscountAmount": 200.00
}
期望响应:
{
  "success": true,
  "code": "10000000",
  "message": "更新成功"
}
```

**测试场景2: 订单状态不允许编辑**
```
请求: PUT /api/v1/orders/ORD001 (已完成的订单)
期望响应:
{
  "success": false,
  "code": "20000002",
  "message": "订单状态不允许编辑"
}
```

**测试场景3: 金额校验失败**
```
请求: PUT /api/v1/orders/ORD001
请求体: {
  "orderTotalAmount": 999999.00  // 前端计算错误的金额
}
期望响应:
{
  "success": false,
  "code": "30000003",
  "message": "金额校验失败"
}
```

## 16. 接口示例数据

### 16.1 订单列表响应示例
```json
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "traceId": "trace-123456",
  "timestamp": 1698765432000,
  "result": {
    "records": [
      {
        "id": 1,
        "orderNo": "ORD001",
        "createdAt": "2023-10-27T10:00:00",
        "ordererName": "张三",
        "ordererPhone": "138****1234",
        "customerName": "张三",
        "customerPhone": "138****1234",
        "customerType": "个人",
        "model": "Myvi",
        "variant": "SE",
        "color": "Red",
        "vin": "PM2BA1234567890",
        "paymentMethod": "全款",
        "orderStatus": "已确认",
        "approvalStatus": "已批准",
        "paymentStatus": "已付",
        "insuranceStatus": "已投保",
        "jpjRegistrationStatus": "已注册",
        "orderTotalAmount": 50000.00
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 16.2 订单详情响应示例
```json
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "traceId": "trace-123456",
  "timestamp": 1698765432000,
  "result": {
    "orderNo": "ORD001",
    "createdAt": "2023-10-27T10:00:00",
    "orderStatus": "已确认",
    "approvalStatus": "已批准",
    "paymentStatus": "已付全款",
    "insuranceStatus": "已投保",
    "jpjRegistrationStatus": "已注册",
    "ordererName": "张三",
    "ordererPhone": "138****1234",
    "customerName": "张三",
    "customerPhone": "138****1234",
    "idType": "身份证",
    "idNumber": "900101-10-1234",
    "email": "<EMAIL>",
    "address": "123, Jalan ABC, Taman DEF, 50000 Kuala Lumpur",
    "state": "Kuala Lumpur",
    "city": "Kuala Lumpur",
    "zipCode": "50000",
    "customerType": "个人",
    "region": "Central",
    "storeCity": "Kuala Lumpur",
    "storeName": "Perodua KL Sentral",
    "salesConsultant": "Ali bin Abu",
    "model": "Myvi",
    "variant": "1.5 SE",
    "color": "Electric Blue",
    "vehiclePrice": 50000.00,
    "numberPlatesFee": 100.00,
    "vin": "PM2BA1234567890",
    "accessories": [
      {
        "category": "电子产品",
        "name": "行车记录仪",
        "price": 300.00,
        "quantity": 1,
        "total": 300.00
      }
    ],
    "invoiceType": "个人发票",
    "invoiceName": "张三",
    "invoicePhone": "138****1234",
    "invoiceAddress": "123, Jalan ABC, Taman DEF, 50000 Kuala Lumpur",
    "rights": [
      {
        "code": "D001",
        "name": "新车折扣",
        "mode": "折扣",
        "discountPrice": 200.00,
        "effectiveDate": "2023-10-27",
        "expiryDate": "2023-11-27"
      }
    ],
    "paymentMethod": "全款支付",
    "loanStatus": "-",
    "depositAmount": 1000.00,
    "loanAmount": 0.00,
    "finalPaymentAmount": 49100.00,
    "policies": [
      {
        "policyNumber": "P123456789",
        "insuranceType": "综合险",
        "insuranceCompany": "ABC Ins.",
        "effectiveDate": "2023-10-28",
        "expiryDate": "2024-10-27",
        "price": 1500.00
      }
    ],
    "insuranceRemarks": "客户要求尽快处理。",
    "otrFees": [
      {
        "invoiceNumber": "JPJ001",
        "item": "首次注册",
        "price": 300.00,
        "effectiveDate": "2023-10-28",
        "expiryDate": null
      }
    ],
    "changeRecords": [
      {
        "operationTime": "2023-10-28T11:00:00",
        "operatorName": "Admin",
        "originalContent": "颜色: Granite Grey",
        "changedContent": "颜色: Electric Blue"
      }
    ],
    "orderTotalAmount": 52000.00,
    "remainingReceivableAmount": 0.00
  }
}
```

### 16.3 权益列表响应示例
```json
{
  "success": true,
  "code": "10000000",
  "message": "查询成功",
  "traceId": "trace-123456",
  "timestamp": 1698765432000,
  "result": [
    {
      "id": 1,
      "code": "D001",
      "name": "新车折扣",
      "mode": "折扣",
      "discountPrice": 200.00,
      "effectiveDate": "2023-10-27",
      "expiryDate": "2023-11-27"
    },
    {
      "id": 2,
      "code": "G002",
      "name": "免费保养一次",
      "mode": "赠品",
      "discountPrice": 0.00,
      "effectiveDate": "2023-10-27",
      "expiryDate": "2024-10-26"
    }
  ]
}
```

## 17. 接口版本管理

### 17.1 版本策略
- 当前版本: v1
- 向后兼容: 新增字段不影响现有接口
- 破坏性变更: 需要升级版本号

### 17.2 版本升级计划
- v1.0: 基础功能实现
- v1.1: 增加批量操作功能
- v2.0: 重构权益管理模块（如有破坏性变更）

## 18. 接口文档维护

### 18.1 文档更新流程
1. 接口变更需要同步更新文档
2. 文档变更需要经过技术评审
3. 文档发布需要通知相关开发人员

### 18.2 文档版本控制
- 使用Git管理文档版本
- 重要变更需要打标签
- 保留历史版本供参考

---

**文档版本**: v1.0
**最后更新**: 2023-10-31
**维护人员**: 开发团队
**审核状态**: 已审核
